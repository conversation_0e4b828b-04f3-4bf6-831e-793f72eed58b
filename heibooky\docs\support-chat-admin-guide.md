# Support Chat System - Admin/Support Staff Implementation Guide

## Overview

This guide provides comprehensive instructions for implementing the frontend interface for admin and support staff members to manage support chat sessions effectively.

## Authentication & Authorization

### WebSocket Connection
- **Endpoint**: `ws://your-domain/ws/support/{chat_id}/`
- **Authentication**: JWT token passed as query parameter
- **Example**: `ws://localhost:8000/ws/support/123e4567-e89b-12d3-a456-************/?token=your_jwt_token`

### Required Permissions
- User must have `is_staff=True` in their profile
- Staff members can access any chat session
- Regular users can only access their own chat sessions

## WebSocket Event Types

### Incoming Events (From Server)

#### 1. Chat Message
```json
{
  "type": "chat.message",
  "message": "Hello, how can I help you?",
  "sender": "support",
  "timestamp": "2024-01-15T10:30:00Z",
  "id": "msg-uuid",
  "status": "in_progress",
  "priority": "medium",
  "attachments": [
    {
      "id": "att-uuid",
      "file_name": "document.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "file_url": "/media/support_attachments/document.pdf"
    }
  ]
}
```

#### 2. Typing Indicator
```json
{
  "type": "typing.indicator",
  "action": "start|stop",
  "user_id": "user-uuid",
  "user_name": "John Doe",
  "is_support": false,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 3. File Upload Notification
```json
{
  "type": "file.uploaded",
  "message_id": "msg-uuid",
  "attachments": [...],
  "sender": "user",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 4. Upload Progress
```json
{
  "type": "upload.progress",
  "message_id": "msg-uuid",
  "progress": 75,
  "sender": "user"
}
```

#### 5. Error Messages
```json
{
  "type": "error",
  "message": "Rate limit exceeded. Please slow down."
}
```

### Outgoing Events (To Server)

#### 1. Send Message
```json
{
  "type": "chat.message",
  "message": "Thank you for contacting support. How can I assist you today?"
}
```

#### 2. Typing Indicators
```json
{
  "type": "typing.start"
}
```

```json
{
  "type": "typing.stop"
}
```

#### 3. File Upload Notification
```json
{
  "type": "file.upload.notify",
  "message_id": "msg-uuid"
}
```

#### 4. Keep-Alive Ping
```json
{
  "type": "ping"
}
```

## Admin Interface Features

### 1. Chat Session Management

#### Multiple Chat Sessions
- Support staff can handle multiple chat sessions simultaneously
- Each chat session should be displayed in a separate tab or window
- Implement visual indicators for active/inactive sessions
- Show unread message counts for each session

#### Chat List Interface
- Display all active chat sessions in a prioritized list
- Sort by priority (urgent, high, medium, low) and last message time
- Show user information, chat status, and last message preview
- Implement real-time updates for new chats and status changes

#### Session Status Management
- **Pending**: New chat waiting for support response
- **In Progress**: Active conversation between user and support
- **Resolved**: Chat marked as completed
- **Sent**: Admin-initiated message sent to user

### 2. Message Management

#### Message Display
- Show message history in chronological order
- Distinguish between user and support messages with visual styling
- Display timestamps and read status for each message
- Support rich text formatting and emoji

#### Message Composition
- Rich text editor with formatting options
- Character counter (max 5000 characters)
- Draft message auto-save functionality
- Message templates for common responses

### 3. File Upload Capabilities

#### Supported File Types
- Documents: PDF, DOC, DOCX, TXT
- Images: JPG, JPEG, PNG
- Maximum file size: 5MB per file

#### Upload Interface
- Drag-and-drop file upload area
- File selection dialog
- Upload progress indicator
- File preview before sending
- Multiple file selection support

#### File Management
- Display uploaded files with download links
- Show file size and type information
- Thumbnail preview for images
- Secure file access with permission validation

### 4. Typing Indicators

#### Implementation Requirements
- Show typing indicator when user is typing
- Auto-hide after 10 seconds of inactivity
- Display user name and support/user status
- Prevent showing indicator to the sender

#### Visual Design
- Animated typing dots or similar indicator
- User avatar and name display
- Distinct styling for support vs. user typing

### 5. Real-time Notifications

#### Desktop Notifications
- Browser notification for new messages
- Sound alerts for urgent priority chats
- Visual badges for unread message counts
- Notification permission handling

#### In-App Notifications
- Toast notifications for system events
- Error message display for failed actions
- Success confirmations for completed actions
- Connection status indicators

## REST API Integration

### Chat Management Endpoints

#### List All Chats
- **GET** `/support/chats/`
- **Response**: Paginated list of chat sessions
- **Filters**: status, priority, user search

#### Get Chat Details
- **GET** `/support/chats/{chat_id}/`
- **Response**: Chat details with user information

#### Update Chat Status
- **POST** `/support/chats/{chat_id}/resolve/`
- **POST** `/support/chats/{chat_id}/prioritize/`
- **Body**: `{"priority": "high"}`

### Message Management Endpoints

#### Get Messages
- **GET** `/support/messages/?chat_id={chat_id}`
- **Response**: Paginated message list with attachments

#### Send Message
- **POST** `/support/messages/`
- **Body**: Form data with message and optional file attachments
- **Content-Type**: `multipart/form-data`

#### Send Message to User (Admin Only)
- **POST** `/support/messages/send_to_user/`
- **Body**: `{"user_id": "uuid", "message": "text", "attachments": [files]}`

### File Upload Endpoints

#### Upload Progress Tracking
- **POST** `/support/messages/upload_progress/`
- **Body**: `{"message_id": "uuid", "progress": 75}`

## Error Handling

### Connection Errors
- WebSocket connection failures
- Authentication token expiration
- Network connectivity issues
- Server unavailability

### Validation Errors
- Empty message content
- File size/type restrictions
- Rate limiting violations
- Permission denied errors

### User Experience Guidelines
- Show clear error messages to users
- Provide retry mechanisms for failed actions
- Implement graceful degradation for offline scenarios
- Display loading states during operations

## Performance Considerations

### Optimization Strategies
- Implement message pagination for large chat histories
- Use virtual scrolling for long message lists
- Lazy load file attachments and previews
- Cache frequently accessed data locally

### Rate Limiting
- Maximum 30 messages per minute per user
- Implement client-side rate limiting indicators
- Show countdown timers for rate limit cooldowns
- Queue messages during rate limit periods

## Security Requirements

### Data Protection
- Validate all user inputs before sending
- Sanitize message content for XSS prevention
- Secure file upload handling
- Implement CSRF protection for API calls

### Access Control
- Verify staff permissions before accessing admin features
- Validate chat access permissions
- Secure file download links with authentication
- Log all admin actions for audit purposes

## Testing Guidelines

### Unit Testing
- Test WebSocket connection handling
- Validate message formatting and display
- Test file upload functionality
- Verify error handling scenarios

### Integration Testing
- Test real-time message delivery
- Validate typing indicator functionality
- Test file upload and download workflows
- Verify notification systems

### User Acceptance Testing
- Test with multiple concurrent chat sessions
- Validate admin workflow efficiency
- Test accessibility compliance
- Verify mobile responsiveness
