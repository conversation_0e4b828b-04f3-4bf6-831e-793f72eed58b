from apps.billing.models import BillingProfile
import datetime

def prepare_account_params(billing_instance, user):
        """Prepare basic parameters for Stripe account creation."""
        return {
            "type": "custom",
            "country": "IT",
            "email": user.email,
            "capabilities": {
                "card_payments": {"requested": True},
                "transfers": {"requested": True},
            },
            "business_type": "company" if billing_instance.recipient_type == BillingProfile.COMPANY else "individual",
        }

def prepare_account_update_data( billing_profile, billing_address, validated_iban, request):
        """Prepare data for updating a Stripe connected account."""
        return {
            "business_type": "company" if billing_profile.recipient_type == BillingProfile.COMPANY else "individual",
            "business_profile": {
                "mcc": "6513",  # Real Estate Rental and Leasing Services
                "name": billing_profile.company_name if billing_profile.recipient_type == BillingProfile.COMPANY 
                       else f"{billing_profile.first_name} {billing_profile.last_name}",
                "product_description": "Property rental services",
                "support_email": "<EMAIL>",
                "support_phone": "+39 ************",
                "url": "https://heibooky.com"
            },
            "individual": {
                "first_name": billing_profile.first_name,
                "last_name": billing_profile.last_name,
                "email": request.user.email,
                "phone": request.user.phone,
                "dob": {
                    "day": billing_profile.date_of_birth.day,
                    "month": billing_profile.date_of_birth.month,
                    "year": billing_profile.date_of_birth.year
                },
                "address": {
                    "line1": f"{billing_address.street_number}",
                    "postal_code": billing_address.postcode,
                    "city": billing_address.city,
                    "country": billing_address.country.code,
                }
            },
            "external_account": {
                "object": "bank_account",
                "country": billing_address.country.code,
                "currency": "eur",
                "account_holder_name": f"{billing_profile.first_name} {billing_profile.last_name}",
                "account_holder_type": "individual" if billing_profile.recipient_type == BillingProfile.NATURAL_PERSON else "company",
                "account_number": validated_iban,
            },
            "tos_acceptance": {
                "date": int(datetime.datetime.now().timestamp()),
                "ip": request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR')),
            }
        }
