from celery import shared_task
from apps.integrations.models import PropertyOnlineCheckIn
from apps.booking.models import Booking
from django.utils import timezone
from datetime import timedelta
import logging
import random
import time

logger = logging.getLogger(__name__)

@shared_task
def sync_guest_data():
    """
    Synchronize guest data with authorities for all properties with online check-in enabled.
    This task should be scheduled to run daily.
    """
    logger.info("Starting guest data synchronization task")
    
    # Get all properties with online check-in enabled
    configs = PropertyOnlineCheckIn.objects.filter(is_enabled=True)
    
    if not configs.exists():
        logger.info("No properties with online check-in enabled found")
        return
    
    logger.info(f"Found {configs.count()} properties with online check-in enabled")
    
    # Process each property
    for config in configs:
        try:
            # Get property ID
            property_id = config.property.id
            
            # Get recent bookings for this property
            recent_bookings = Booking.objects.filter(
                property=config.property,
                check_in_date__gte=timezone.now() - timedelta(days=7),
                check_in_date__lte=timezone.now() + timedelta(days=7)
            )
            
            logger.info(f"Processing {recent_bookings.count()} recent bookings for property {property_id}")
            
            # Process ISTAT reporting
            if config.istat_enabled:
                _sync_with_istat(config.property, recent_bookings)
            
            # Process Alloggati Web reporting
            if config.alloggati_enabled:
                _sync_with_alloggati(config.property, recent_bookings)
            
            # Update last sync time
            config.last_sync = timezone.now()
            
            # Calculate next sync time
            config.next_sync = timezone.now().replace(hour=22, minute=0, second=0, microsecond=0)
            if timezone.now() >= config.next_sync:
                config.next_sync += timedelta(days=1)
            
            # Save the configuration
            config.save(update_fields=['last_sync', 'next_sync'])
            
            logger.info(f"Successfully synchronized guest data for property {property_id}")
            
        except Exception as e:
            logger.error(f"Error synchronizing guest data for property {config.property.id}: {str(e)}")
    
    logger.info("Guest data synchronization task completed")


def _sync_with_istat(property_instance, bookings):
    """
    Synchronize guest data with ISTAT.
    
    Args:
        property_instance: Property instance
        bookings: QuerySet of bookings to synchronize
    """
    # Simulate processing delay
    time.sleep(random.uniform(0.5, 1.5))
    
    # Mock implementation - log the synchronization
    logger.info(f"Synchronizing {bookings.count()} bookings with ISTAT for property {property_instance.id}")
    
    # Simulate random success/failure
    if random.random() > 0.1:  # 90% success rate
        logger.info(f"Successfully synchronized guest data with ISTAT for property {property_instance.id}")
    else:
        logger.error(f"Failed to synchronize guest data with ISTAT for property {property_instance.id}")
        raise Exception("ISTAT synchronization failed")


def _sync_with_alloggati(property_instance, bookings):
    """
    Synchronize guest data with Alloggati Web.
    
    Args:
        property_instance: Property instance
        bookings: QuerySet of bookings to synchronize
    """
    # Simulate processing delay
    time.sleep(random.uniform(0.5, 1.5))
    
    # Mock implementation - log the synchronization
    logger.info(f"Synchronizing {bookings.count()} bookings with Alloggati Web for property {property_instance.id}")
    
    # Simulate random success/failure
    if random.random() > 0.1:  # 90% success rate
        logger.info(f"Successfully synchronized guest data with Alloggati Web for property {property_instance.id}")
    else:
        logger.error(f"Failed to synchronize guest data with Alloggati Web for property {property_instance.id}")
        raise Exception("Alloggati Web synchronization failed")
