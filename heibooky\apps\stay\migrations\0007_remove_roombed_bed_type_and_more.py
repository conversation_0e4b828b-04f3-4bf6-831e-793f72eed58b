# Generated by Django 5.1.2 on 2025-05-02 05:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0006_room_bathroom_quntity'),
    ]

    operations = [
        # migrations.RemoveField(
        #     model_name='roombed',
        #     name='bed_type',
        # ),
        migrations.AlterUniqueTogether(
            name='roombed',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='roombed',
            name='room',
        ),
        migrations.AddField(
            model_name='room',
            name='bed_config',
            field=models.JSONField(blank=True, default=dict, help_text='bed structure fot room configuration', null=True),
        ),
        migrations.DeleteModel(
            name='BedType',
        ),
        migrations.DeleteModel(
            name='RoomBed',
        ),
    ]
