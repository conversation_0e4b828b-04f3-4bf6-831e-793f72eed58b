import logging
from django.template.loader import render_to_string
from django.conf import settings
from xhtml2pdf import pisa
from django.core.files.base import ContentFile
from io import BytesIO
from apps.billing.models import BillingProfile
from assets.translations import TRANSLATIONS_IT

logger = logging.getLogger(__name__)

def generate_pdf_invoice(invoice):
    """
    Generates a PDF invoice using xhtml2pdf and saves it to the invoice instance
    Args:
        invoice: Invoice instance
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logo_url = "https://heibookybucket.fra1.cdn.digitaloceanspaces.com/heibookybucket/media/logo_on_blue.png"
        # Prepare context data
        billing = BillingProfile.objects.get(owner=invoice.owner)
        context = {
            'billing': billing,
            'invoice': invoice,
            'payout': invoice.payout,
            'booking': invoice.payout.booking,
            'customer': invoice.payout.customer,
            'company_logo': logo_url,
            'company_name': settings.COMPANY_NAME,
            'company_vat': settings.COMPANY_VAT_CODE,
            'translations': TRANSLATIONS_IT
        }

        # Render HTML template
        html_string = render_to_string('invoices/payout_invoice.html', context)
        
        # Create PDF
        pdf_file = BytesIO()
        pisa_status = pisa.CreatePDF(
            html_string,
            dest=pdf_file,
            encoding='utf-8'
        )
        
        if pisa_status.err:
            logger.error('Error creating PDF invoice')
            return False
            
        # Save to invoice instance
        filename = f"invoice_{invoice.progressive_number}.pdf"
        invoice.pdf_file.save(filename, ContentFile(pdf_file.getvalue()), save=True)
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to generate PDF invoice: {str(e)}")
        return False
