from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.stay.models import Property, Location
from apps.integrations.tasks.su_api import (
    send_success_notification,
    send_failure_notification,
    send_partial_failure_notification
)
from unittest.mock import patch

User = get_user_model()

class OnboardingNotificationsTest(TestCase):
    """Test cases for the onboarding notification functions"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email="<EMAIL>",
            name="Test User",
            password="testpassword"
        )

        # Create a test property
        self.location = Location.objects.create(
            street="Test Street",
            city="Test City",
            country="IT",
            latitude=41.9028,
            longitude=12.4964
        )

        self.property = Property.objects.create(
            name="Test Property",
            property_type=Property.HOTEL,  # Using the correct field name and value
            location=self.location
        )
        self.property.staffs.add(self.user)

    @patch('services.notification.handlers.GeneralNotificationHandler.send_notification')
    def test_send_success_notification(self, mock_send_notification):
        """Test that success notifications are sent correctly"""
        # Call the function
        send_success_notification(self.user, self.property)

        # Check that the notification was sent
        mock_send_notification.assert_called_once_with(channels=['websocket', 'database', 'email'])

    @patch('services.notification.handlers.GeneralNotificationHandler.send_notification')
    def test_send_failure_notification(self, mock_send_notification):
        """Test that failure notifications are sent correctly"""
        # Call the function
        send_failure_notification(self.user, self.property)

        # Check that the notification was sent
        mock_send_notification.assert_called_once()

    @patch('services.notification.handlers.GeneralNotificationHandler.send_notification')
    def test_send_partial_failure_notification(self, mock_send_notification):
        """Test that partial failure notifications are sent correctly"""
        # Create test results
        results = {
            'property': True,
            'rates': False,
            'rooms': True,
            'photos': False
        }

        # Call the function
        send_partial_failure_notification(self.user, self.property, results)

        # Check that the notification was sent
        mock_send_notification.assert_called_once()
