from rest_framework import serializers
from apps.integrations.models import Payout, Invoice, Notification, DownloadableTemplate


class InvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Invoice
        fields = ['id', 'payout', 'pdf_file', 'progressive_number', 'created_at']

class PayoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payout
        fields = [
            'id', 'customer', 'stripe_payment_intent_id', 'amount', 
            'currency', 'status', 'created_at', 'updated_at', 'invoice_status'
        ]

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'title', 'message', 'is_read', 'created_at']

class DownloadableTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadableTemplate
        fields = ['id', 'title', 'description', 'file', 'category', 'created_at']