from celery import shared_task
from services.email import EmailService
import logging
from django.utils.translation import gettext as _
from .utils import (
    get_location_data,
    get_device_info,
    should_notify_location_change
)
import json
from django.utils import timezone
from services.email import AccountEmailService
from django.core.exceptions import ObjectDoesNotExist
from .models import User, UserProfile

logger = logging.getLogger(__name__)

@shared_task
def send_welcome_email(email):
    """
    Task to send a welcome email to a new user.
    This task is executed asynchronously.
    """
    try:
        # Initialize email service
        email_service = EmailService()
        email_service.send_welcome_email(email)
    except Exception as e:
        logger.error(f"Failed to send welcome email: {str(e)}", exc_info=True)

        raise e  # Optionally re-raise the exception to notify Ce<PERSON>y of the failure
    return True

@shared_task
def send_password_reset_email(email):
    """
    Task to send a password reset email to the user.
    This task is executed asynchronously.
    """
    try:
        # Initialize email service
        email_service = EmailService()
        email_service.send_password_reset_email(email)
    except Exception as e:
        logger.error(f"Failed to send password reset email: {str(e)}", exc_info=True)

        raise e
    return True

@shared_task
def send_verification_email(email):
    """
    Task to send a verification email to the user.
    This task is executed asynchronously.
    """
    try:
        # Initialize email service
        email_service = EmailService()
        email_service.send_verification_email(email)
    except Exception as e:
        logger.error(f"Failed to send verification email: {str(e)}", exc_info=True)

        raise e
    return True

def get_previous_location(profile):
    """Retrieve and parse previous location data"""
    if not profile.last_login_location:
        return None
        
    loc = profile.last_login_location
    if isinstance(loc, str):
        try:
            return json.loads(loc)
        except json.JSONDecodeError:
            return loc
    return loc

def send_notification(user, location, device, login_time):
    """Handle notification email sending"""
    try:
        email_service = AccountEmailService()
        email_service.send_new_login_location_email(
            user.email,
            {
                'user_name': user.name,
                'login_time': login_time.strftime("%Y-%m-%d %H:%M:%S"),
                'location': location['location_string'],
                'device': device.get('device_string', 'Unknown'),
                'ip_address': location['ip_address']
            }
        )
    except Exception as e:
        logger.error(f"Failed to send notification email: {str(e)}")

def update_profile_location(profile, location):
    """Update profile with new location data"""
    try:
        profile.last_login_location = location
        profile.save(update_fields=['last_login_location'])
    except Exception as e:
        logger.error(f"Failed to update profile location: {str(e)}")

@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def login_location_task(self, user_id, ip_address, user_agent, login_time):
    """Enhanced login location processing task"""
    try:
        # Get user and ensure they exist
        try:
            user = User.objects.select_related('profile').get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User {user_id} not found for login location processing")
            return

        # Get location and device info
        login_time = timezone.datetime.fromisoformat(login_time)
        location_string, location_data = get_location_data(ip_address)
        device_info = get_device_info(user_agent)

        # Prepare current location data
        current_location = {
            'location_string': location_string,
            'login_time': login_time.isoformat(),
            'ip_address': ip_address,
            **(location_data or {})
        }

        # Get previous location and check for changes
        previous_location = user.profile.last_login_location
        location_changed = should_notify_location_change(previous_location, current_location)

        if location_changed:
            try:
                notification_data = {
                    'user_name': user.name,
                    'login_time': login_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'location': location_string,
                    'device': device_info.get('device_string', 'Unknown device'),
                    'previous_location': (
                        previous_location.get('location_string', 'Unknown')
                        if isinstance(previous_location, dict)
                        else str(previous_location or 'Unknown')
                    ),
                    'ip_address': ip_address
                }
                
                email_service = AccountEmailService()
                email_service.send_new_login_location_email(user.email, notification_data)
                
            except Exception as e:
                logger.error(f"Failed to send location notification: {str(e)}")
                # Don't retry the whole task for notification failure
                
        # Update profile location
        try:
            user.profile.last_login_location = current_location
            user.profile.save(update_fields=['last_login_location'])
        except Exception as e:
            logger.error(f"Failed to update login location: {str(e)}")
            raise self.retry(exc=e)

    except Exception as e:
        logger.error(f"Login location task failed: {str(e)}", exc_info=True)
        raise self.retry(exc=e)