from channels.generic.websocket import AsyncJsonWebsocketConsumer
from channels.db import database_sync_to_async
import logging
import os
import asyncio
import json
from datetime import datetime, timedelta
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import TokenError
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from django.core.exceptions import ValidationError
from urllib.parse import parse_qs

logger = logging.getLogger(__name__)

class NotificationConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        self.user = self.scope["user"]
        logger.debug(f"Connection attempt by user: {self.user}")

        if not self.user.is_authenticated:
            logger.warning("Unauthenticated connection attempt")
            await self.close()
            return

        self.group_name = f"notifications_{self.user.id}"
        logger.debug(f"User authenticated, group name: {self.group_name}")

        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()
        logger.debug("Connection accepted")

    async def disconnect(self, close_code):
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )

    async def receive_json(self, content, **kwargs):
        action = content.get("action")
        logger.debug(f"Received action: {action} from user {self.user.id}")

        if action == "mark_as_read":
            notification_ids = content.get("notification_ids", [])
            for notif_id in notification_ids:
                await self.mark_notification_as_read(notif_id)
        elif action == "ping":
            # Respond to ping with a pong to keep the connection alive
            logger.debug(f"Received ping from user {self.user.id}, sending pong")
            await self.send_json({"type": "pong"})

    async def mark_notification_as_read(self, notif_id):
        from apps.integrations.models import Notification
        try:
            notification = await database_sync_to_async(Notification.objects.get)(
                id=notif_id,
                user=self.user
            )
            if not notification.is_read:
                notification.is_read = True
                await database_sync_to_async(notification.save)()
                # Broadcast the update
                await self.channel_layer.group_send(
                    self.group_name,
                    {
                        "type": "notification_update",
                        "data": {
                            "id": str(notification.id),
                            "is_read": True
                        }
                    }
                )
        except Notification.DoesNotExist:
            pass

    async def notification_message(self, event):
        """Handle incoming notification messages"""
        await self.send_json(event["message"])

    async def send_notification(self, event):
        """Handle send_notification message type"""
        await self.send_json({
            "type": "notification",
            "message": event["data"]
        })

    async def notification_update(self, event):
        """Handle notification update messages"""
        await self.send_json({
            "type": "notification_update",
            "data": event["data"]
        })

class SupportChatConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        # Get query parameters from the scope
        query_string = self.scope.get('query_string', b'').decode()
        query_params = parse_qs(query_string)
        
        # Extract token from query parameters
        token = query_params.get('token', [None])[0]
        logger.debug(f"Support chat connection attempt with token: {token is not None}")

        if not token:
            logger.warning("No authentication token provided in support chat connection")
            await self.close(code=4001)
            return

        # Authenticate the user using the token
        try:
            user = await self.get_user_from_token(token)
            if not user:
                logger.warning(f"Invalid token provided for support chat connection")
                await self.close(code=4002)
                return
            
            self.user = user
        except Exception as e:
            logger.error(f"Error authenticating token: {str(e)}")
            await self.close(code=4003)
            return

        # Get the chat_id from the URL path
        try:
            self.chat_id = self.scope["url_route"]["kwargs"].get("chat_id")
            if not self.chat_id:
                logger.warning("No chat_id provided in URL path")
                await self.close(code=4004)
                return

            # Validate the chat_id
            chat = await self.get_chat(self.chat_id)
            if not chat:
                logger.warning(f"Chat with ID {self.chat_id} not found")
                await self.close(code=4005)
                return
                
            self.target_user_id = str(chat['user_id'])
            
            # Check permissions
            if not self.user.is_staff and str(self.user.id) != self.target_user_id:
                logger.warning(f"User {self.user.id} attempted to access another user's chat {self.chat_id}")
                await self.close(code=4006)
                return
                
        except Exception as e:
            logger.error(f"Error getting chat details: {str(e)}")
            await self.close(code=4007)
            return

        # Create a unique group name for this chat
        self.group_name = f"support_chat_{self.target_user_id}"
        logger.debug(f"User connecting to support chat, group name: {self.group_name}")

        # Join the chat group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()
        logger.debug(f"Support chat connection accepted for chat {self.chat_id}")

    @database_sync_to_async
    def get_user_from_token(self, token):
        """
        Get user from JWT token
        """
        User = get_user_model()
        try:
            # Verify the token and get the user ID
            access_token = AccessToken(token)
            user_id = access_token.get('user_id')
            
            if not user_id:
                logger.warning("Token does not contain user_id")
                return None
            
            # Fetch the user using the ID from the token
            user = User.objects.get(id=user_id)
            return user
        except TokenError as e:
            logger.warning(f"Invalid token: {str(e)}")
            return None
        except User.DoesNotExist:
            logger.warning(f"User from token not found")
            return None
        except Exception as e:
            logger.error(f"Unexpected error authenticating token: {str(e)}")
            raise
    
    @database_sync_to_async
    def get_chat(self, chat_id):
        from apps.support.models import Chat
        try:
            chat = Chat.objects.filter(id=chat_id).values('id', 'user_id', 'status', 'priority').first()
            return chat
        except Exception:
            return None

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
            logger.debug(f"Support chat disconnected from {self.group_name}")

        # Clear any typing indicators for this user
        if hasattr(self, 'user') and hasattr(self, 'group_name'):
            await self._clear_typing_indicator()

    async def receive_json(self, content, **kwargs):
        """Handle incoming messages from WebSocket"""
        try:
            action = content.get("type")
            logger.debug(f"Received support chat action: {action} from user {self.user.id}")

            # Rate limiting check
            if not await self._check_rate_limit():
                await self.send_json({
                    "type": "error",
                    "message": "Rate limit exceeded. Please slow down."
                })
                return

            if action == "chat.message":
                await self._handle_chat_message(content)
            elif action == "typing.start":
                await self._handle_typing_start()
            elif action == "typing.stop":
                await self._handle_typing_stop()
            elif action == "file.upload.notify":
                await self._handle_file_upload_notification(content)
            elif action == "ping":
                await self.send_json({"type": "pong"})
            else:
                logger.warning(f"Unknown action received: {action}")
                await self.send_json({
                    "type": "error",
                    "message": "Unknown action type"
                })

        except Exception as e:
            logger.error(f"Error in receive_json: {str(e)}")
            await self.send_json({
                "type": "error",
                "message": "Internal server error"
            })

    async def _handle_chat_message(self, content):
        """Handle incoming chat messages with validation"""
        try:
            message_text = content.get("message", "").strip()

            # Validate message content
            if not message_text:
                await self.send_json({
                    "type": "error",
                    "message": "Message cannot be empty"
                })
                return

            if len(message_text) > 5000:  # Max message length
                await self.send_json({
                    "type": "error",
                    "message": "Message too long (max 5000 characters)"
                })
                return

            # Save the message to the database
            message = await self.save_support_message(
                self.chat_id,
                message_text,
                is_from_support=self.user.is_staff
            )

            # Broadcast the message to the group
            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "chat_message",
                    "message": message["message"],
                    "sender": "support" if message["is_from_support"] else "user",
                    "timestamp": message["created_at"],
                    "id": message["id"],
                    "status": message["chat_status"],
                    "priority": message["chat_priority"]
                }
            )

            # Clear typing indicator after sending message
            await self._clear_typing_indicator()

        except Exception as e:
            logger.error(f"Error handling chat message: {str(e)}")
            await self.send_json({
                "type": "error",
                "message": "Failed to send chat message"
            })

    async def chat_message(self, event):
        """Handle chat message event and send to WebSocket"""
        message_data = {
            "type": "chat.message",
            "message": event["message"],
            "sender": event["sender"],
            "timestamp": event["timestamp"],
            "id": event["id"],
            "status": event["status"],
            "priority": event["priority"]
        }

        # Include attachments if available
        if "attachments" in event:
            message_data["attachments"] = event["attachments"]

        await self.send_json(message_data)

    @database_sync_to_async
    def save_support_message(self, chat_id, message, is_from_support):
        """Save support message with enhanced validation and error handling"""
        from apps.support.models import Chat, SupportMessage
        from django.db import transaction

        try:
            with transaction.atomic():
                # Get the chat with select_for_update to prevent race conditions
                chat = Chat.objects.select_for_update().get(id=chat_id)

                # Validate message content
                if not message or not message.strip():
                    raise ValidationError("Message cannot be empty")

                if len(message) > 5000:
                    raise ValidationError("Message too long (max 5000 characters)")

                # Update chat status based on the message sender
                if is_from_support:
                    if chat.status in ['pending', 'resolved']:
                        chat.status = 'in_progress'
                else:
                    if chat.status == 'resolved':
                        chat.status = 'pending'

                # Update last_message_at to current time
                chat.last_message_at = timezone.now()
                chat.save(update_fields=['status', 'updated_at', 'last_message_at'])

                # Create the message
                support_message = SupportMessage.objects.create(
                    chat=chat,
                    message=message.strip(),
                    is_from_support=is_from_support
                )

                # Return the message details
                return {
                    "id": str(support_message.id),
                    "message": support_message.message,
                    "is_from_support": support_message.is_from_support,
                    "created_at": support_message.created_at.isoformat(),
                    "chat_status": chat.status,
                    "chat_priority": chat.priority
                }

        except Chat.DoesNotExist:
            logger.error(f"Chat with ID {chat_id} not found")
            raise ValidationError("Chat not found")
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error saving support message: {str(e)}")
            raise

    async def _handle_typing_start(self):
        """Handle typing start event with auto-timeout"""
        try:
            # Set typing indicator in cache with expiration
            cache_key = f"typing_{self.group_name}_{self.user.id}"
            cache.set(cache_key, True, timeout=10)  # 10 seconds timeout

            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "typing_indicator",
                    "action": "start",
                    "user_id": str(self.user.id),
                    "user_name": getattr(self.user, 'name', 'Unknown'),
                    "is_support": self.user.is_staff,
                    "timestamp": timezone.now().isoformat()
                }
            )

            # Schedule auto-stop after 10 seconds
            asyncio.create_task(self._auto_stop_typing())

        except Exception as e:
            logger.error(f"Error handling typing start: {str(e)}")

    async def _handle_typing_stop(self):
        """Handle typing stop event"""
        try:
            await self._clear_typing_indicator()
        except Exception as e:
            logger.error(f"Error handling typing stop: {str(e)}")

    async def _clear_typing_indicator(self):
        """Clear typing indicator for this user"""
        try:
            # Remove from cache
            cache_key = f"typing_{self.group_name}_{self.user.id}"
            cache.delete(cache_key)

            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "typing_indicator",
                    "action": "stop",
                    "user_id": str(self.user.id),
                    "user_name": getattr(self.user, 'name', 'Unknown'),
                    "is_support": self.user.is_staff,
                    "timestamp": timezone.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Error clearing typing indicator: {str(e)}")

    async def _auto_stop_typing(self):
        """Automatically stop typing indicator after timeout"""
        await asyncio.sleep(10)  # Wait 10 seconds

        # Check if user is still typing
        cache_key = f"typing_{self.group_name}_{self.user.id}"
        if not cache.get(cache_key):
            return  # Already stopped

        # Auto-stop typing
        await self._clear_typing_indicator()

    async def _handle_file_upload_notification(self, content):
        """Handle file upload completion notification with validation"""
        try:
            message_id = content.get("message_id")
            if not message_id:
                await self.send_json({
                    "type": "error",
                    "message": "message_id is required for file upload notification"
                })
                return

            # Validate message belongs to this chat and user has permission
            if not await self._validate_message_access(message_id):
                await self.send_json({
                    "type": "error",
                    "message": "Access denied to this message"
                })
                return

            # Get the message with attachments
            message_data = await self.get_message_with_attachments(message_id)
            if message_data:
                await self.channel_layer.group_send(
                    self.group_name,
                    {
                        "type": "file_uploaded",
                        "message_id": message_id,
                        "attachments": message_data["attachments"],
                        "sender": "support" if self.user.is_staff else "user",
                        "timestamp": message_data["created_at"]
                    }
                )
            else:
                await self.send_json({
                    "type": "error",
                    "message": "Message not found or no attachments"
                })

        except Exception as e:
            logger.error(f"Error handling file upload notification: {str(e)}")
            await self.send_json({
                "type": "error",
                "message": "Failed to process file upload notification"
            })

    async def _check_rate_limit(self):
        """Check if user is within rate limits"""
        try:
            cache_key = f"rate_limit_{self.user.id}"
            current_count = cache.get(cache_key, 0)

            # Allow 30 messages per minute
            if current_count >= 30:
                return False

            cache.set(cache_key, current_count + 1, timeout=60)
            return True

        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            return True  # Allow on error to avoid blocking legitimate users

    @database_sync_to_async
    def _validate_message_access(self, message_id):
        """Validate user has access to the message"""
        try:
            from apps.support.models import SupportMessage
            message = SupportMessage.objects.select_related('chat').get(id=message_id)

            # Check if message belongs to this chat
            if str(message.chat.id) != self.chat_id:
                return False

            # Check user permissions
            if not self.user.is_staff and str(message.chat.user.id) != str(self.user.id):
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating message access: {str(e)}")
            return False

    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket"""
        # Don't send typing indicator to the sender
        if str(self.user.id) != event["user_id"]:
            await self.send_json({
                "type": "typing.indicator",
                "action": event["action"],
                "user_id": event["user_id"],
                "user_name": event["user_name"],
                "is_support": event["is_support"],
                "timestamp": event["timestamp"]
            })

    async def file_uploaded(self, event):
        """Send file upload notification to WebSocket"""
        await self.send_json({
            "type": "file.uploaded",
            "message_id": event["message_id"],
            "attachments": event["attachments"],
            "sender": event["sender"],
            "timestamp": event["timestamp"]
        })

    async def upload_progress(self, event):
        """Send upload progress notification to WebSocket"""
        # Only send progress to the uploader
        if event.get("user_id") == str(self.user.id):
            await self.send_json({
                "type": "upload.progress",
                "message_id": event["message_id"],
                "progress": event["progress"],
                "sender": event["sender"]
            })

    @database_sync_to_async
    def get_message_with_attachments(self, message_id):
        """Get message with attachments for WebSocket notification"""
        try:
            from apps.support.models import SupportMessage

            # Get message with related data
            message = SupportMessage.objects.select_related('chat').prefetch_related('attachments').get(id=message_id)

            # Validate user has access to this message
            if not self.user.is_staff and str(message.chat.user.id) != str(self.user.id):
                logger.warning(f"User {self.user.id} attempted to access message {message_id} without permission")
                return None

            attachments = []
            for attachment in message.attachments.all():
                # Validate file exists
                file_url = None
                if attachment.file and hasattr(attachment.file, 'url'):
                    try:
                        file_url = attachment.file.url
                    except Exception as e:
                        logger.warning(f"Error getting file URL for attachment {attachment.id}: {str(e)}")

                attachments.append({
                    "id": str(attachment.id),
                    "file_name": attachment.file_name,
                    "file_size": attachment.file_size,
                    "content_type": attachment.content_type,
                    "file_url": file_url
                })

            return {
                "id": str(message.id),
                "message": message.message,
                "attachments": attachments,
                "created_at": message.created_at.isoformat()
            }

        except SupportMessage.DoesNotExist:
            logger.error(f"Message with ID {message_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error getting message with attachments: {str(e)}")
            return None
