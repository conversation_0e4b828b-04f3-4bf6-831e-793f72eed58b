import pytest
from django.core.management import call_command
from django.contrib.auth import get_user_model
from apps.users.models import UserProfile
from io import StringIO

User = get_user_model()

@pytest.mark.django_db
def test_user_profile_created_for_regular_user():
    """Test that a user profile is created for a regular user."""
    # Create a regular user
    user = User.objects.create_user(
        email="<EMAIL>",
        name="Test User",
        password="password123"
    )
    
    # Check if profile was created
    assert UserProfile.objects.filter(user=user).exists()
    assert UserProfile.objects.count() == 1

@pytest.mark.django_db
def test_user_profile_created_for_superuser():
    """Test that a user profile is created for a superuser."""
    # Create a superuser
    superuser = User.objects.create_superuser(
        email="<EMAIL>",
        name="Admin User",
        password="adminpass123"
    )
    
    # Check if profile was created
    assert UserProfile.objects.filter(user=superuser).exists()
    assert UserProfile.objects.count() == 1
