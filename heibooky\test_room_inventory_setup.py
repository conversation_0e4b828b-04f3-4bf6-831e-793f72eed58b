"""
Test script to validate the room onboarding inventory setup functionality.
This script tests:
1. The signal handler correctly detects is_onboarded changes
2. The inventory task is triggered when a room becomes onboarded
3. The management command works correctly
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

from django.test import TestCase
from unittest.mock import patch, MagicMock
from apps.stay.models import Room, Property
from apps.booking.tasks.inventory import set_room_inventory_for_year
from datetime import datetime, timedelta
from django.utils import timezone


def test_room_onboarding_signal():
    """Test that the signal correctly triggers inventory setup when room is onboarded."""
    print("Testing room onboarding signal...")
    
    # Get an existing room that is not onboarded
    try:
        room = Room.objects.filter(is_onboarded=False).first()
        if not room:
            print("No non-onboarded rooms found. Creating a test scenario...")
            # If no non-onboarded room exists, we'll simulate the scenario
            return test_inventory_task_directly()
            
        print(f"Testing with room: {room.id} ({room.get_room_type_display()}) in property: {room.property.name}")
        
        # Mock the inventory task to prevent actual API calls during testing
        with patch('apps.booking.tasks.inventory.set_room_inventory_for_year.delay') as mock_task:
            # Update the room to be onboarded
            room.is_onboarded = True
            room.save()
            
            # Check if the task was called
            if mock_task.called:
                print("✓ Signal correctly triggered inventory task when room was onboarded!")
                print(f"  Task called with: {mock_task.call_args}")
                return True
            else:
                print("✗ Signal did not trigger inventory task")
                return False
                
    except Exception as e:
        print(f"Error testing signal: {str(e)}")
        return False


def test_inventory_task_directly():
    """Test the inventory task directly."""
    print("\nTesting inventory task directly...")
    
    try:
        # Get an onboarded room
        room = Room.objects.filter(is_onboarded=True, property__is_onboarded=True).first()
        if not room:
            print("No onboarded rooms found for testing")
            return False
            
        print(f"Testing inventory task with room: {room.id}")
        
        # Mock the update_inventory function to prevent actual API calls
        with patch('apps.booking.tasks.inventory.update_inventory') as mock_update_inventory:
            mock_update_inventory.return_value = {"Status": "Success", "Message": "Test success"}
            
            # Call the task directly
            result = set_room_inventory_for_year(str(room.id))
            
            if result.get('status') == 'success':
                print("✓ Inventory task executed successfully!")
                print(f"  Result: {result}")
                
                # Check if update_inventory was called with correct parameters
                if mock_update_inventory.called:
                    call_args = mock_update_inventory.call_args
                    inventory_data = call_args[1]['inventory_data']  # keyword argument
                    
                    print(f"  update_inventory called with inventory_data: {inventory_data}")
                    
                    # Validate inventory data format
                    if isinstance(inventory_data, list) and len(inventory_data) > 0:
                        data_item = inventory_data[0]
                        if 'from' in data_item and 'to' in data_item and 'roomstosell' in data_item:
                            print("✓ Inventory data format is correct!")
                            
                            # Check if the date range is approximately one year
                            from_date = datetime.strptime(data_item['from'], '%Y-%m-%d').date()
                            to_date = datetime.strptime(data_item['to'], '%Y-%m-%d').date()
                            days_difference = (to_date - from_date).days
                            
                            if 360 <= days_difference <= 370:  # Allow some tolerance
                                print(f"✓ Date range is correct: {days_difference} days")
                                return True
                            else:
                                print(f"✗ Date range is incorrect: {days_difference} days")
                        else:
                            print("✗ Inventory data missing required fields")
                    else:
                        print("✗ Inventory data format is incorrect")
                else:
                    print("✗ update_inventory was not called")
            else:
                print(f"✗ Inventory task failed: {result}")
                
    except Exception as e:
        print(f"Error testing inventory task: {str(e)}")
        return False
    
    return False


def test_date_range_calculation():
    """Test that the date range calculation is correct."""
    print("\nTesting date range calculation...")
    
    try:
        # Calculate expected dates
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=365)
        
        print(f"Expected date range: {start_date} to {end_date}")
        print(f"Days in range: {(end_date - start_date).days}")
        
        # Test the format_date_range function
        from apps.booking.tasks.inventory import format_date_range
        
        formatted_range = format_date_range(start_date, end_date)
        
        if isinstance(formatted_range, list) and len(formatted_range) == 1:
            item = formatted_range[0]
            if (item.get('from') == start_date.strftime('%Y-%m-%d') and 
                item.get('to') == end_date.strftime('%Y-%m-%d')):
                print("✓ Date range formatting is correct!")
                return True
            else:
                print(f"✗ Date range formatting is incorrect: {item}")
        else:
            print(f"✗ Date range format is incorrect: {formatted_range}")
            
    except Exception as e:
        print(f"Error testing date range: {str(e)}")
        
    return False


if __name__ == "__main__":
    print("=== Room Inventory Setup Testing ===\n")
    
    success_count = 0
    total_tests = 3
    
    # Run tests
    if test_date_range_calculation():
        success_count += 1
        
    if test_inventory_task_directly():
        success_count += 1
        
    if test_room_onboarding_signal():
        success_count += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ All tests passed! The implementation is working correctly.")
    else:
        print("Some tests failed. Please check the implementation.")
