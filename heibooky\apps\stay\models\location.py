from django.db import models
import uuid
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator

class Location(models.Model):
    """
    Model to store location data including coordinates.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    street = models.CharField(max_length=255, verbose_name=_("Street Address"))
    post_code = models.CharField(max_length=10, verbose_name=_("Postal Code"))
    city = models.CharField(max_length=100, verbose_name=_("City"))
    country = models.CharField(max_length=100, verbose_name=_("Country"))
    latitude = models.FloatField(verbose_name=_("Latitude"),
        validators=[MinValueValidator(-90), MaxValueValidator(90)]
    )
    longitude = models.FloatField(verbose_name=_("Longitude"),
        validators=[MinValueValidator(-180), MaxValueValidator(180)]
    )
    is_editable = models.BooleanField(default=True, verbose_name=_("Is Editable"))

    def __str__(self):
        return f"{self.street}, {self.city}, {self.country}"

    class Meta:
        indexes = [
            models.Index(fields=['city', 'country']),
        ]
