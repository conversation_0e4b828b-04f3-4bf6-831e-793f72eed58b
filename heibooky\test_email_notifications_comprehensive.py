#!/usr/bin/env python
"""
Comprehensive test script for email notification system
This script tests the complete email notification flow for support messages
"""

import os
import sys
import django
from unittest.mock import patch, MagicMock
import time

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\BackTrack\\heibooky')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

from apps.support.models import Chat, SupportMessage
from apps.users.models import User
from services.email.email_service import EmailService
from apps.support.tasks import send_admin_to_user_notification, send_user_to_admin_notification
from django.core import mail
from django.test.utils import override_settings
from django.template.loader import render_to_string

def test_email_notifications_comprehensive():
    """Test the complete email notification system"""
    print("Testing Email Notification System - Comprehensive")
    print("=" * 60)
    
    # Test 1: Check email service initialization
    print("\n1. Testing EmailService initialization...")
    try:
        email_service = EmailService()
        print("✓ EmailService initialized successfully")
        print(f"✓ From email: {email_service.from_email}")
        print(f"✓ Language: {email_service.language}")
        print(f"✓ Available translations: {len(email_service.translations)} keys")
    except Exception as e:
        print(f"✗ EmailService initialization failed: {e}")
        return False
    
    # Test 2: Check required methods exist
    print("\n2. Testing EmailService methods...")
    required_methods = [
        'send_admin_message_notification',
        'send_user_message_notification',
        'send_email'
    ]
    
    for method in required_methods:
        if hasattr(email_service, method):
            print(f"✓ {method} method exists")
        else:
            print(f"✗ {method} method missing")
            return False
    
    # Test 3: Create test data
    print("\n3. Creating test data...")
    try:
        # Create or get test user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'name': 'Test User',
                'phone': '+1234567890',
                'is_verified': True
            }
        )
        print(f"✓ Test user {'created' if created else 'retrieved'}: {user.email}")
        
        # Create test chat
        chat, created = Chat.objects.get_or_create(
            user=user,
            defaults={'priority': 'medium'}
        )
        print(f"✓ Test chat {'created' if created else 'retrieved'}: ID {chat.id}")
        
    except Exception as e:
        print(f"✗ Failed to create test data: {e}")
        return False
    
    # Test 4: Test template rendering
    print("\n4. Testing email template rendering...")
    try:
        # Test admin message notification template
        admin_context = {
            'user_name': user.name,
            'message': 'Test admin response message',
            'chat_url': 'https://frontend.heibooky.com/dashboard/chat',
            'priority': 'Media',
            'site_name': 'Heibooky',
            'logo_url': 'https://test.com/logo.jpg',
            'translations': email_service.translations,
        }
        
        admin_html = render_to_string('emails/admin_message_notification.html', admin_context)
        admin_txt = render_to_string('emails/admin_message_notification.txt', admin_context)
        
        if admin_html and admin_txt:
            print("✓ Admin message notification templates render successfully")
            print(f"  - HTML template: {len(admin_html)} characters")
            print(f"  - Text template: {len(admin_txt)} characters")
        else:
            print("✗ Admin message notification templates failed to render")
            return False
        
        # Test user message notification template
        user_context = {
            'user_name': user.name,
            'user_email': user.email,
            'message': 'Test user message',
            'priority': 'Media',
            'attachments': [],
            'admin_url': 'https://admin.heibooky.com/support/message/1/',
            'site_name': 'Heibooky',
            'logo_url': 'https://test.com/logo.jpg',
            'translations': email_service.translations,
        }
        
        user_html = render_to_string('emails/user_message_notification.html', user_context)
        user_txt = render_to_string('emails/user_message_notification.txt', user_context)
        
        if user_html and user_txt:
            print("✓ User message notification templates render successfully")
            print(f"  - HTML template: {len(user_html)} characters")
            print(f"  - Text template: {len(user_txt)} characters")
        else:
            print("✗ User message notification templates failed to render")
            return False
            
    except Exception as e:
        print(f"✗ Template rendering failed: {e}")
        return False
    
    # Test 5: Test email sending with mock
    print("\n5. Testing email sending functionality...")
    try:
        with patch('django.core.mail.EmailMultiAlternatives.send') as mock_send:
            mock_send.return_value = True
            
            # Create test message from admin
            admin_message = SupportMessage.objects.create(
                chat=chat,
                message='Test admin message',
                is_from_support=True,
                is_read=False
            )
            
            # Test admin to user notification
            success = email_service.send_admin_message_notification(admin_message)
            if success and mock_send.called:
                print("✓ Admin message notification sent successfully")
                print(f"  - Email send method called {mock_send.call_count} time(s)")
            else:
                print("✗ Admin message notification failed")
                return False
            
            # Reset mock
            mock_send.reset_mock()
            
            # Create test message from user
            user_message = SupportMessage.objects.create(
                chat=chat,
                message='Test user message',
                is_from_support=False,
                is_read=False
            )
            
            # Test user to admin notification
            success = email_service.send_user_message_notification(user_message)
            if success and mock_send.called:
                print("✓ User message notification sent successfully")
                print(f"  - Email send method called {mock_send.call_count} time(s)")
            else:
                print("✗ User message notification failed")
                return False
                
    except Exception as e:
        print(f"✗ Email sending test failed: {e}")
        return False
    
    # Test 6: Test Celery tasks
    print("\n6. Testing Celery tasks...")
    try:
        # Test admin to user task
        with patch('apps.support.tasks.EmailService') as mock_service:
            mock_instance = MagicMock()
            mock_instance.send_admin_message_notification.return_value = True
            mock_service.return_value = mock_instance
            
            result = send_admin_to_user_notification.apply_async(
                args=[str(admin_message.id)],
                countdown=0  # No delay for testing
            )
            
            # Wait for task completion
            task_result = result.get(timeout=10)
            if task_result:
                print("✓ Admin to user Celery task executed successfully")
            else:
                print("✗ Admin to user Celery task failed")
                return False
        
        # Test user to admin task  
        with patch('apps.support.tasks.EmailService') as mock_service:
            mock_instance = MagicMock()
            mock_instance.send_user_message_notification.return_value = True
            mock_service.return_value = mock_instance
            
            result = send_user_to_admin_notification.delay(str(user_message.id))
            
            # Wait for task completion
            task_result = result.get(timeout=10)
            if task_result:
                print("✓ User to admin Celery task executed successfully")
            else:
                print("✗ User to admin Celery task failed")
                return False
                
    except Exception as e:
        print(f"✗ Celery task test failed: {e}")
        return False
    
    # Test 7: Test is_read field functionality
    print("\n7. Testing is_read field functionality...")
    try:
        # Create unread admin message
        unread_message = SupportMessage.objects.create(
            chat=chat,
            message='Unread admin message',
            is_from_support=True,
            is_read=False
        )
        
        # Create read admin message
        read_message = SupportMessage.objects.create(
            chat=chat,
            message='Read admin message',
            is_from_support=True,
            is_read=True
        )
        
        # Test that only unread messages trigger notifications
        with patch('apps.support.tasks.EmailService') as mock_service:
            mock_instance = MagicMock()
            mock_instance.send_admin_message_notification.return_value = True
            mock_service.return_value = mock_instance
            
            # Test unread message
            result_unread = send_admin_to_user_notification.apply_async(
                args=[str(unread_message.id)],
                countdown=0
            )
            
            # Test read message
            result_read = send_admin_to_user_notification.apply_async(
                args=[str(read_message.id)],
                countdown=0
            )
            
            unread_result = result_unread.get(timeout=10)
            read_result = result_read.get(timeout=10)
            
            if unread_result and read_result:
                print("✓ is_read field functionality working correctly")
                print("  - Unread message processed")
                print("  - Read message skipped appropriately")
            else:
                print("✗ is_read field functionality failed")
                return False
                
    except Exception as e:
        print(f"✗ is_read field test failed: {e}")
        return False
    
    # Test 8: Translation coverage
    print("\n8. Testing translation coverage...")
    try:
        required_translations = [
            'support_response_received',
            'support_team_responded', 
            'support_response_intro',
            'support_message_from_team',
            'view_conversation',
            'continue_conversation_help',
            'need_immediate_help',
            'new_support_message',
            'hello',
            'priority',
            'message',
            'user_name',
            'user_email'
        ]
        
        missing_translations = []
        for key in required_translations:
            if key not in email_service.translations:
                missing_translations.append(key)
        
        if not missing_translations:
            print("✓ All required translations are available")
            print(f"  - {len(required_translations)} translations checked")
        else:
            print(f"✗ Missing translations: {missing_translations}")
            return False
            
    except Exception as e:
        print(f"✗ Translation test failed: {e}")
        return False
    
    # Cleanup
    print("\n9. Cleaning up test data...")
    try:
        SupportMessage.objects.filter(chat=chat).delete()
        chat.delete()
        print("✓ Test data cleaned up successfully")
    except Exception as e:
        print(f"⚠ Cleanup warning: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED! Email notification system is working correctly!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = test_email_notifications_comprehensive()
        if not success:
            print("\n❌ Some tests failed. Please check the output above.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
