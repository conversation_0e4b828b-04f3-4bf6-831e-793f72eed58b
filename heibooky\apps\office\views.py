from rest_framework import viewsets, permissions
from rest_framework.response import Response
from apps.stay.models import Property
from apps.booking.models import Booking, Reservation
from .serializers import PropertyStatisticsSerializer
from django.db.models import Sum, Count, Avg, F, ExpressionWrapper, fields
from django.utils import timezone
from datetime import timedelta
from django.shortcuts import get_object_or_404
import uuid
from rest_framework.exceptions import ValidationError

class PropertyStatisticsViewSet(viewsets.ViewSet):
    permission_classes = [permissions.IsAdminUser]
    
    def retrieve(self, request, pk=None):
        """
            Retrieve property statistics for a specific property.
        """
        # Validate the UUID format
        try:
            uuid.UUID(str(pk), version=4)
        except ValueError:  
            return Response(
                {'error': 'Invalid Property ID format. Must be a valid UUID.'},
                status=400
            )
        
        property_obj = get_object_or_404(Property, pk=pk)

        # Check if the property is active
        if not property_obj.is_active:
            raise ValidationError("Property is not active.")
        
        # Get date range from query params or default to last 30 days
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        try:
            if 'start_date' in request.query_params:
                start_date = timezone.datetime.strptime(request.query_params['start_date'], '%Y-%m-%d').date()
            if 'end_date' in request.query_params:
                end_date = timezone.datetime.strptime(request.query_params['end_date'], '%Y-%m-%d').date()
        except ValueError:
            return Response(
            {'error': 'Invalid date format. Please use YYYY-MM-DD format.'},
            status=400
            )

        # Calculate statistics
        bookings = Booking.objects.filter(
            property=property_obj,
            booking_date__date__range=[start_date, end_date]
        )
        
        total_bookings = bookings.count()
        total_rooms = property_obj.rooms.filter(is_active=True).aggregate(
            total_quantity=Sum('quantity'))['total_quantity'] or 0
        
        # Calculate revenue and occupancy metrics
        stats = bookings.aggregate(
            total_revenue=Sum('reservation_data__total_price'),
            avg_stay=Avg(
                ExpressionWrapper(
                    F('checkout_date') - F('checkin_date'),
                    output_field=fields.DurationField()
                )
            )
        )
        
        # Calculate occupancy rate
        total_days = (end_date - start_date).days
        total_room_days = total_rooms * total_days
        occupied_room_days = bookings.aggregate(
            total_days=Sum(
                ExpressionWrapper(
                    F('checkout_date') - F('checkin_date'),
                    output_field=fields.DurationField()
                )
            )
        )['total_days'] or timedelta()
        
        occupancy_rate = (occupied_room_days.days / total_room_days * 100) if total_room_days > 0 else 0
        
        # Calculate ADR and RevPAR
        total_revenue = stats['total_revenue'] or 0
        adr = total_revenue / occupied_room_days.days if occupied_room_days.days > 0 else 0
        revpar = total_revenue / (total_rooms * total_days) if total_rooms * total_days > 0 else 0
        
        data = {
            'id': property_obj.id,
            'name': property_obj.name,
            'total_bookings': total_bookings,
            'total_revenue': total_revenue,
            'average_stay_duration': stats['avg_stay'].days if stats['avg_stay'] else 0,
            'occupancy_rate': round(occupancy_rate, 2),
            'average_daily_rate': round(adr, 2),
            'revenue_per_available_room': round(revpar, 2)
        }
        
        serializer = PropertyStatisticsSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data)
