from rest_framework import permissions
from rest_framework.exceptions import ValidationError
from apps.billing.utils import check_billing_complete
from apps.stay.models import StaffRole, Room, Photo, PropertyMetadata, GuestArrivalInfo
from apps.stay.utils import validate_ownership
from apps.pricing.models import RatePlan, RoomRate
from apps.booking.models import Booking, BookingBlock
from typing import Optional
import uuid
import logging

logger = logging.getLogger(__name__)

class BasePropertyPermission(permissions.BasePermission):
    """Base class for property-related permissions with common utilities."""

    def validate_uuid(self, id_str: str, field_name: str) -> uuid.UUID:
        """Validate UUID format and return UUID object if valid."""
        try:
            return uuid.UUID(str(id_str), version=4)
        except (ValueError, AttributeError, TypeError):
            raise ValidationError({
                "error": f"Invalid {field_name} format. Must be a valid UUID."
            })

    def get_property_id(self, request, view) -> Optional[str]:
        """Extract property ID from request data, query params, or view kwargs."""
        return (
            view.kwargs.get('pk') or
            request.data.get('property') or
            request.query_params.get('property') or
            request.data.get('property_id') or
            request.query_params.get('property_id')
        )

    def check_staff_permission(self, user_id: str, property_id: uuid.UUID, 
                              permission_name: str) -> bool:
        """Check if user has the specified permission for a property."""
        staff_permissions = set(
            StaffRole.objects.filter(
                user_id=user_id,
                property_id=property_id,
                permissions__name=permission_name,
                is_active=True
            ).values_list('permissions__name', flat=True)
        )
        
        return permission_name in staff_permissions

    def has_property_permission(self, request, property_id: uuid.UUID, permission_name: str) -> bool:
        """Check if user has permission for a property (either as owner or with specific staff role)."""
        if not property_id:
            return False

        return (
            self.check_staff_permission(request.user.id, property_id, permission_name) or
            validate_ownership(request.user, property_id)
        )

class IsStaffPermission(permissions.BasePermission):
    """
    Custom permission to allow access only to staff users.
    """
    message = None  # Override default message

    def has_permission(self, request, view):
        try:
            if request.user.is_staff or request.user.is_admin:
                return True
            else:
                self.message = {"error": "You do not have permission to perform this action."}
                return False
        except Exception as e:
            logger.error(f"IsStaffPermission error: {str(e)}")
            self.message = {"error": "An error occurred while checking permissions."}
            return False


class VerifiedOwnerPermission(BasePropertyPermission):
    """
    Custom permission to ensure the user has an associated BillingProfile.
    """
    message = None  # Override default message

    def has_permission(self, request, view):
        try:
            property_id = request.data.get('property_id') or view.kwargs.get('property_id')
            if not property_id:
                self.message = {"error": "Property ID is required"}
                return False

            valid_property_id = self.validate_uuid(property_id, "property ID")

            # Check if the user has a BillingProfile
            if not check_billing_complete(request.user):
                self.message = {"error": "User has no complete billing profile"}
                return False
                
            is_owner = validate_ownership(request.user, valid_property_id)
            if not is_owner:
                self.message = {"error": "User is not the owner of this property"}
            return is_owner

        except Exception as e:
            logger.error(f"Verified owner permission error: {str(e)}")
            self.message = e.detail
            return False


class PropertyConfigPermission(BasePropertyPermission):
    """Permission for property configuration operations."""

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        try:
            property_id = self.get_property_id(request, view)
            photo_ids = request.data.get('ids')
            
            if not property_id and not photo_ids:
                return False
                
            if photo_ids:
                # Get unique properties from all photos
                photos = Photo.objects.filter(id__in=photo_ids)
                if not photos.exists():
                    request.permission_error = {"error": "No photos found with the given IDs"}
                    return False
                    
                photo_properties = set(photos.values_list('property_id', flat=True))
                if len(photo_properties) > 1:
                    return False  # Photos belong to different properties
                property_id = photo_properties.pop()
                
            valid_uuid = self.validate_uuid(property_id, "property ID")
            return self.has_property_permission(request, valid_uuid, 'property_config')
            
        except Exception as e:
            logger.error(f"Property config permission error: {str(e)}")
            return False

class PropertyMetadataPermission(BasePropertyPermission):
    """Permission for property metadata operations."""

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        try:
            metadata_id = view.kwargs.get('pk')
            if not metadata_id:
                request.permission_error = {"error": "Metadata ID is required."}
                return False

            try:
                valid_id = self.validate_uuid(metadata_id, "Property Metadata ID")
                property_id = PropertyMetadata.objects.get(id=valid_id).property.id
            except PropertyMetadata.DoesNotExist:
                request.permission_error = {"error": "Property metadata not found."}
                return False

            valid_property_id = self.validate_uuid(property_id, "property ID")
            has_permission = self.has_property_permission(request, valid_property_id, 'property_config')

            if not has_permission:
                request.permission_error = {
                    "error": "You don't have permission to modify this property's metadata."
                }
            return has_permission

        except ValidationError as e:
            request.permission_error = e.detail
            return False
        except Exception as e:
            logger.error(f"Property metadata permission error: {str(e)}")
            request.permission_error = {"error": "An error occurred while checking permissions."}
            return False

class GuestArrivalInfoPermission(BasePropertyPermission):
    """
    Permission to allow users with guest_arrival permission to modify guest arrival settings.
    """
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        property_id = request.data.get('property') 

        guest_arrival_id = view.kwargs.get('pk')        
        if not property_id and not guest_arrival_id:
            return False
        try:
            if property_id:
                valid_property_id = self.validate_uuid(property_id, "property ID")
            elif guest_arrival_id:
                try:
                    guest_arrival = GuestArrivalInfo.objects.get(id=guest_arrival_id)
                    valid_property_id = guest_arrival.property.id
                except GuestArrivalInfo.DoesNotExist:
                    raise ValidationError({"error": "Guest arrival info not found."})
            return self.has_property_permission(request, valid_property_id, 'guest_arrival')
        
        except ValidationError as e:
            # Let the ValidationError propagate to be handled by DRF
            raise e
        except Exception as e:
            logger.error(f"Guest arrival permission error: {str(e)}")
            return False

class RoomConfigPermission(BasePropertyPermission):
    """
    Permission to allow users with room_config permission to modify room settings.
    """
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
            
        property_id = (
            request.data.get('property') or 
            request.query_params.get('property') or 
            request.data.get('property_id') or 
            request.query_params.get('property_id')
        )

        room_id = (
            view.kwargs.get('pk') or 
            view.kwargs.get('room_id') or 
            request.data.get('room') or 
            request.query_params.get('room') or 
            request.data.get('room_id') or 
            request.query_params.get('room_id')
        )
        
        if not property_id and not room_id:
            return False

        try:
            if property_id:
                valid_property_id = self.validate_uuid(property_id, "property ID")
            elif room_id:
                valid_room_id = self.validate_uuid(room_id, "Room ID")
                try:
                    property_id = Room.objects.get(id=valid_room_id).property.id
                    valid_property_id = uuid.UUID(str(property_id))
                except Room.DoesNotExist:
                    raise ValidationError({"error": "Room not found."})
            
            return self.has_property_permission(request, valid_property_id, 'room_config')
        
        except ValidationError as e:
            # Let the ValidationError propagate to be handled by DRF
            raise e
        except Exception as e:
            logger.error(f"Room config permission error: {str(e)}")
            return False


class RatesPermission(BasePropertyPermission):
    """
    Permission to allow users with rates_manage permission or property owners to modify rates.
    """
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        property_id = (
            request.data.get('property') or 
            request.query_params.get('property')
        )
        rate_plan_id = (
            view.kwargs.get('pk') or
            request.data.get('rate_plan') or
            request.query_params.get('rate_plan')
        )
        
        if not property_id and not rate_plan_id:
            return False

        try:
            valid_property_id = None
            if property_id:
                valid_property_id = self.validate_uuid(property_id, "property ID")
            elif rate_plan_id:
                try:
                    rate_plan = RatePlan.objects.get(id=rate_plan_id)
                    valid_property_id = rate_plan.property.id
                except RatePlan.DoesNotExist:
                    raise ValidationError({"rate_plan": "Rate plan not found."})
            
            return self.has_property_permission(request, valid_property_id, 'rates_manage')
        
        except ValidationError as e:
            # Let the ValidationError propagate to be handled by DRF
            raise e
        except Exception as e:
            logger.error(f"Rates permission error: {str(e)}")
            return False


class RoomRatesPermission(BasePropertyPermission):
    """
    Permission to allow users with rates_manage permission to modify room rates.
    """
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
            
        property_id = request.query_params.get('property')
        room_id = request.data.get('room')
        room_rate_id = view.kwargs.get('pk')
        
        if not property_id and not room_id and not room_rate_id:
            return False
        
        try:
            valid_property_id = None
            if property_id:
                valid_property_id = self.validate_uuid(property_id, "property ID")
            elif room_id:
                valid_room_id = self.validate_uuid(room_id, "room ID")
                try:
                    valid_property_id = Room.objects.get(id=valid_room_id).property.id
                except Room.DoesNotExist:
                    raise ValidationError({"error": "Room not found."})
            elif room_rate_id:
                try:
                    room_rate = RoomRate.objects.get(id=room_rate_id)
                    valid_property_id = room_rate.room.property.id
                except RoomRate.DoesNotExist:
                    raise ValidationError({"error": "Room rate not found."})
            
            valid_permission = self.has_property_permission(request, valid_property_id, 'rates_manage')
            return valid_permission
        except ValidationError as e:
            # Let the ValidationError propagate to be handled by DRF
            raise e
        except Exception as e:
            logger.error(f"Room rates permission error: {str(e)}")
            return False


class BookingPermission(BasePropertyPermission):
    """
    Permission to allow users with booking_manage permission to handle bookings.
    """
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
            
        property_id = request.data.get('property')
        booking_block_id = view.kwargs.get('pk')
        booking_id =  request.data.get('booking_id')
        
        if not property_id and not booking_block_id and not booking_id:
            return False

        try:
            valid_property_id = None
            if property_id:
                valid_property_id = self.validate_uuid(property_id, "property ID")
            elif booking_block_id:
                try:
                    booking_block = BookingBlock.objects.get(id=booking_block_id)
                    valid_property_id = booking_block.property.id
                except BookingBlock.DoesNotExist:
                    raise ValidationError({"error": "Booking block not found."})
            elif booking_id:
                try:
                    booking = Booking.objects.get(id=booking_id)
                    valid_property_id = booking.property.id
                except Booking.DoesNotExist:
                    raise ValidationError({"error": "Booking not found."})

            return self.has_property_permission(request, valid_property_id, 'booking_manage')
        
        except ValidationError as e:
            # Let the ValidationError propagate to be handled by DRF
            raise e
        except Exception as e:
            logger.error(f"Booking permission error: {str(e)}")
            return False


class ViewOnlyPermission(permissions.BasePermission):
    """Permission that only allows read operations."""
    
    def has_permission(self, request, view):
        return request.method in permissions.SAFE_METHODS