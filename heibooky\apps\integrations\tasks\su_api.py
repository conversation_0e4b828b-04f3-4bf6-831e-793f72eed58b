from celery import shared_task
from services.su_api import (
    onboard_property, onboard_rooms,
    upload_photos, associate_images,
    connect_rate_plan, associate_room_rate,
)
from apps.integrations.utils import (
    log_action, build_property_data, build_room_data, build_room_update_data,
    build_rate_plan_data, build_room_rate_data
)
from apps.stay.models import Property, Room, Photo
from apps.pricing.models import RatePlan, RoomRate
from services.email import PropertyEmailService
from services.notification.handlers import GeneralNotificationHandler
import logging
from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist
from apps.users.models import User

logger = logging.getLogger(__name__)


@shared_task(bind=True, autoretry_for=(Exception,), max_retries=3, default_retry_delay=300)
def onboarding_task(self, property_id, user_id):
    """
    Main Celery task for property onboarding process
    Handles all steps with proper error handling and retries

    Args:
        self: The Celery task instance (used for retries)
        property_id: The ID of the property to onboard
        user_id: The ID of the user who initiated the onboarding
    """
    try:
        user = User.objects.get(id=user_id)
        property_instance = Property.objects.get(id=property_id)

        action = "update" if property_instance.is_onboarded else "create"
        request_type = "Overlay" if action == "update" else "New"

        results = {
            'property': False,
            'rates': False,
            'rooms': False,
            'photos': False
        }

        try:
            # Step 1: Property Onboarding
            results['property'] = handle_property(request_type=request_type, user=user, action=action, property_instance=property_instance)

            # Step 2: Rate Plan Deployment
            results['rates'] = handle_rate_plans(property_instance_id=property_instance.id, user_id=user.id, action=action)

            # Step 3: Room Onboarding
            results['rooms'] = handle_rooms(user_id=user.id, action=action, property_id=property_instance.id) and handle_room_rates(property_instance_id=property_instance.id, user_id=user.id, action=action)

            # Step 4: Photo Deployment
            results['photos'] = handle_photos(property_instance.id, user.id, action)

            # Finalize
            if all(results.values()):
                send_success_notification(user, property_instance)
            else:
                send_partial_failure_notification(user, property_instance, results)

            return {
                "status": "completed",
                "results": results,
                "property_id": property_id
            }

        except Exception as e:
            logger.error(f"Onboarding failed for property {property_id}: {str(e)}")
            send_failure_notification(user, property_instance)
            raise  # Will trigger retry based on task configuration

    except ObjectDoesNotExist as e:
        logger.error(f"Onboarding failed - invalid user/property: {str(e)}")
        return {"status": "failed", "error": "Invalid user or property"}

@shared_task
def handle_property(request_type, user, action="update", property_id=None, property_instance=None):
    """
    Handle property onboarding with proper error handling

    Args:
        request_type (str): Type of request ("New" or "Overlay")
        user: User object or user ID
        action (str): Action type ("create", "update")
        property_id: ID of the property to process (if property_instance not provided)
        property_instance: Property instance (optional, will be fetched using property_id if not provided)
    """
    try:
        # Get user object if user_id is provided
        if not isinstance(user, User) and user is not None:
            try:
                user = User.objects.get(id=user)
            except User.DoesNotExist:
                logger.error(f"User with ID {user} not found")
                return False

        # Get property instance if property_id is provided
        if property_id and not property_instance:
            try:
                property_instance = Property.objects.get(id=property_id)
            except Property.DoesNotExist:
                logger.error(f"Property with ID {property_id} not found")
                return False

        # Validate we have a property instance
        if not property_instance:
            logger.error("No property instance or property_id provided")
            return False

        # Build property data and send to API
        property_data = build_property_data(property_instance, request_type)
        property_response = onboard_property(property_data)

        if property_response.get("Status") == "Success":
            with transaction.atomic():
                property_instance.is_onboarded = True
                property_instance.save(update_fields=['is_onboarded'])
                log_success('property', user, action, property_response, property_instance)
            return True
        else:
            raise Exception(f"Property onboarding failed: {property_response.get('Message', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Property onboarding error: {str(e)}")
        return False


@shared_task
def handle_rate_plans(property_instance_id, user_id, action, update_type="new"):
    """
    Handle rate plan deployment with proper error handling

    Args:
        property_instance_id: ID of the property
        user_id: ID of the user initiating the action
        action: Type of action ("create", "update")
        update_type: Type of update ("new", "modify", "activate", "deactivate")
    """
    try:
        # Get property instance
        try:
            property_instance = Property.objects.get(id=property_instance_id)
        except Property.DoesNotExist:
            logger.error(f"Property with ID {property_instance_id} not found")
            return False

        # Get user
        try:
            user = User.objects.get(id=user_id) if user_id else None
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            user = None

        success = True
        rate_plans = RatePlan.objects.filter(
            property=property_instance,
            is_onboarded=False
        )

        logger.info(f"Processing {rate_plans.count()} rate plans for property {property_instance_id}")

        for plan in rate_plans:
            plan_data = build_rate_plan_data(plan, update_type)
            response = connect_rate_plan(plan_data)

            if response.get("Status") == "Success":
                with transaction.atomic():
                    plan.is_onboarded = True
                    plan.save(update_fields=['is_onboarded'])
                log_success('rate', user, action, response, plan)
                logger.info(f"Successfully processed rate plan {plan.id}")
            else:
                success = False
                log_failure('rate', user, action, response, plan)
                logger.error(f"Failed to process rate plan {plan.id}: {response}")

        return success

    except Exception as e:
        logger.error(f"Rate plan error: {str(e)}", exc_info=True)
        return False

@shared_task
def handle_room_rates(property_instance_id, user_id, action):
    """
    Handle room rate deployment

    Args:
        property_instance_id: ID of the property
        user_id: ID of the user initiating the action
        action: Type of action ("create", "update")
    """
    try:
        # Get user
        try:
            user = User.objects.get(id=user_id) if user_id else None
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            user = None

        # Get property instance
        try:
            property_instance = Property.objects.get(id=property_instance_id)
        except Property.DoesNotExist:
            logger.error(f"Property with ID {property_instance_id} not found")
            return False

        rate_success = True
        rates = RoomRate.objects.filter(
            room__property=property_instance,
            is_onboarded=False
        )

        logger.info(f"Processing {rates.count()} room rates for property {property_instance_id}")

        for rate in rates:
            rate_data = build_room_rate_data(rate)
            response = associate_room_rate(rate_data)

            if response.get("Status") == "Success":
                with transaction.atomic():
                    rate.is_onboarded = True
                    rate.ticket_id = response.get("TicketId")
                    rate.save(update_fields=['is_onboarded', 'ticket_id'])
                log_success('room_rate', user, action, response, rate)
                logger.info(f"Successfully processed room rate {rate.id}")
            else:
                rate_success = False
                log_failure('room_rate', user, action, response, rate)
                logger.error(f"Failed to process room rate {rate.id}: {response}")

        return rate_success

    except Exception as e:
        logger.error(f"Room rate processing error: {str(e)}", exc_info=True)
        return False

@shared_task
def handle_rooms(user_id, action, property_id=None, room_id=None, update_type=None):
    """
    Handle room deployment with improved error handling and input validation.

    Args:
        user_id (int): ID of the user initiating the action
        action (str): Type of action ("create", "update")
        property_id (UUID, optional): ID of the property for batch processing
        room_id (UUID, optional): ID of specific room for single updates
        update_type (str, optional): Type of update ("amenities_update", "is_active_update", "general_update")
    """
    try:
        # Fetch user
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return False

        # Handle single room update
        if room_id:
            try:
                room = Room.objects.select_related('property').get(id=room_id)
                if update_type:
                    room_data = build_room_update_data(room, update_type)
                else:
                    room_data = build_room_data(room.property.id, [room])

                room_response = onboard_rooms(room_data)
                success = room_response.get("Status") == "Success"

                if success and not room.is_onboarded:
                    # Use individual save to trigger signals
                    room.is_onboarded = True
                    room.save(update_fields=['is_onboarded'])

                log_action(
                    user=user,
                    property_id=room.property.id,
                    action=action,
                    description=f"Room {update_type or action}",
                    status="successful" if success else "failed",
                    details={"response": room_response}
                )
                return success

            except Room.DoesNotExist:
                logger.error(f"Room with ID {room_id} not found")
                return False
            except Exception as e:
                logger.error(f"Error processing room {room_id}: {str(e)}")
                return False

        # Handle property-wide room processing
        elif property_id:
            try:
                # Process rooms for the entire property
                rooms = Room.objects.filter(
                    property_id=property_id,
                    is_onboarded=False,
                    is_active=True
                )

                if not rooms.exists():
                    return True

                room_data = build_room_data(property_id, rooms)
                room_response = onboard_rooms(room_data)
                success = room_response.get("Status") == "Success"

                if success:
                    # Update each room individually to trigger signals
                    for room in rooms:
                        room.is_onboarded = True
                        room.save(update_fields=['is_onboarded'])

                log_action(
                    user=user,
                    property_id=property_id,
                    action=action,
                    description="Bulk room processing",
                    status="successful" if success else "failed",
                    details={"response": room_response}
                )
                return success

            except Exception as e:
                logger.error(f"Error processing rooms for property {property_id}: {str(e)}")
                return False

        return False

    except Exception as e:
        logger.error(f"Unexpected error in handle_rooms task: {str(e)}")
        return False

@shared_task
def handle_photos(property_id, user_id, action):
    """
    Handle photo upload and association

    Args:
        property_id: ID of the property whose photos need to be processed
        user_id: ID of the user initiating the action (can be int or User object)
        action: Type of action ("create", "update", "delete")
    """
    try:
        # Get user object if user_id is provided
        if not isinstance(user_id, User):
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                logger.error(f"User with ID {user_id} not found")
                return False
        else:
            user = user_id

        # Get property instance
        try:
            property_instance = Property.objects.get(id=property_id)
        except Property.DoesNotExist:
            logger.error(f"Property with ID {property_id} not found")
            return False

        # First handle non-onboarded photos
        new_photos = Photo.objects.filter(
            property=property_instance,
            is_onboarded=False
        )

        success = True

        logger.info(f"Processing {new_photos.count()} non-onboarded photos for property {property_id}")

        # Upload new photos if they exist
        if new_photos.exists():
            # Batch processing
            batch_size = 5

            for i in range(0, len(new_photos), batch_size):
                batch = list(new_photos[i:i+batch_size])
                logger.info(f"Processing batch {i//batch_size + 1} with {len(batch)} photos")

                try:
                    response = upload_photos(property_instance, batch)

                    if response.get("Status") != "Success":
                        success = False
                        logger.error(f"Failed to upload photo batch: {response}")
                        log_failure('photos', user, action, response, None, property_instance.id)
                        break

                    # Update onboarded status for successful batch
                    with transaction.atomic():
                        for photo in batch:
                            photo.is_onboarded = True
                            photo.save(update_fields=['is_onboarded'])
                    log_success('photos', user, action, response, property_instance)

                except Exception as batch_error:
                    logger.error(f"Error processing batch: {str(batch_error)}")
                    success = False
                    log_failure('photos', user, action, {'error': str(batch_error)}, None, property_instance.id)
                    break

        # Get all onboarded photo IDs regardless of previous upload success
        photo_ids = list(Photo.objects.filter(
            property=property_instance,
            is_onboarded=True
        ).values_list('id', flat=True))

        # Associate photos if there are any onboarded photos
        if photo_ids:
            logger.info(f"Associating {len(photo_ids)} photos with property {property_id}")
            try:
                associate_response = associate_images(property_instance, photo_ids)
                if associate_response.get("Status") != "Success":
                    success = False
                    logger.error(f"Failed to associate photos: {associate_response}")
                    log_failure('photo_association', user, action, associate_response, None, property_instance.id)
                else:
                    log_success('photo_association', user, action, associate_response, property_instance)
            except Exception as assoc_error:
                logger.error(f"Error associating photos: {str(assoc_error)}")
                success = False
                log_failure('photo_association', user, action, {'error': str(assoc_error)}, None, property_instance.id)

        return success

    except Exception as e:
        logger.error(f"Photo processing error: {str(e)}", exc_info=True)
        try:
            # Only log failure if we have the necessary objects
            if 'user' in locals() and 'property_instance' in locals():
                log_failure('photos', user, action, {'error': str(e)}, None, property_instance.id)
        except Exception:
            pass
        return False

# Helper functions
def log_success(entity_type, user, action, response, instance=None, property_id=None):
    """Centralized success logging"""
    log_action(
        user=user,
        property_id=property_id if property_id else getattr(instance, 'property_id', instance.id),
        action=action,
        description=f"Successfully processed {entity_type}",
        status="successful",
        details={"response": response}
    )

def log_failure(entity_type, user, action, response, instance=None, property_id=None):
    """Centralized failure logging"""
    log_action(
        user=user,
        property_id=property_id if property_id else getattr(instance, 'property_id', instance.id),
        action=action,
        description=f"Failed to process {entity_type}",
        status="failed",
        details={"error": response}
    )

def send_success_notification(user, property_instance):
    """
    Send success notifications in Italian using the GeneralNotificationHandler.

    Args:
        user: The user to notify
        property_instance: The property that was successfully onboarded
    """
    # Create email service for email notifications
    email_service = PropertyEmailService()

    # Create notification handler with Italian messages
    handler = GeneralNotificationHandler(
        users=user,
        title="Onboarding Completato",
        message=f"{property_instance.name} è stato registrato con successo",
        email_service=email_service,
        email_template="property_onboarded",
        email_context={
            'property': property_instance
        }
    )

    # Send notifications through all channels
    handler.send_notification(channels=['websocket', 'database', 'email'])

def send_failure_notification(user, property_instance):
    """
    Send failure notifications in Italian using the GeneralNotificationHandler.

    Args:
        user: The user to notify
        property_instance: The property that failed to onboard
    """
    handler = GeneralNotificationHandler(
        users=user,
        title="Onboarding Fallito",
        message=f"Impossibile completare l'onboarding di {property_instance.name} - si prega di contattare l'assistenza"
    )

    # Send notifications through websocket and database only
    handler.send_notification()

def send_partial_failure_notification(user, property_instance, results):
    """
    Handle partial failure notifications in Italian using the GeneralNotificationHandler.

    Args:
        user: The user to notify
        property_instance: The property that was partially onboarded
        results: Dictionary of onboarding step results
    """
    # Translate step names to Italian
    step_translations = {
        'property': 'proprietà',
        'rates': 'tariffe',
        'rooms': 'camere',
        'photos': 'foto'
    }

    # Get failed steps and translate them
    failed_steps = [step_translations.get(k, k) for k, v in results.items() if not v]

    # Create Italian message
    message = f"Onboarding parzialmente riuscito per {property_instance.name}. Problemi con: {', '.join(failed_steps)}"

    # Create and send notification
    handler = GeneralNotificationHandler(
        users=user,
        title="Onboarding Parzialmente Riuscito",
        message=message
    )

    handler.send_notification()