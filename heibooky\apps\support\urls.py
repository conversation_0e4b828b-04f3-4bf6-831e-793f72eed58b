from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import ChatViewSet, MessageViewSet, support_analytics_summary, support_analytics_timeseries

router = DefaultRouter()
router.register(r'chats', ChatViewSet, basename='chat')
router.register(r'messages', MessageViewSet, basename='message')

urlpatterns = [
    path('', include(router.urls)),
    path('analytics/summary/', support_analytics_summary, name='support-analytics-summary'),
    path('analytics/timeseries/', support_analytics_timeseries, name='support-analytics-timeseries'),
]