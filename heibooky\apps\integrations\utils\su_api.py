from apps.stay.models import *
from apps.pricing.models import RatePlan
from apps.integrations.models import SUAPIActionLog
from django.utils import timezone
from django.core.exceptions import ValidationError


def log_action(user, property_id, action, description, status, details=None):
    """
    Helper function to log actions to SUAPIActionLog.
    
    Args:
        user: User performing the action
        property_id: ID of the property involved
        action: Type of action (create, update, delete)
        description: Description of the action
        status: Status of the action (successful, failed)
        details: Additional details about the action (optional)
    """
    SUAPIActionLog.objects.create(
        user=user,
        property_id=property_id,
        action=action,
        description=description,
        status=status,
        timestamp=timezone.now(),
        details=details or {}
    )

def build_property_data(property_instance, request_type):
    """
    Formats the property instance data into the SU API structure.
    :param type: Notification type (e.g., "New" or "Update").
    :return: JSON data structure required by SU API.
    :raises ValueError: If required data is missing.
    """
    # Query property and related data, and raise ValueError if any data is missing
    try:
        metadata = PropertyMetadata.objects.get(property=property_instance.id)
        arrival_info = GuestArrivalInfo.objects.get(property=property_instance.id)
        location = Location.objects.get(property=property_instance.id)
        amenities = PropertyAmenity.objects.filter(property=property_instance.id)
    except Property.DoesNotExist:
        raise ValueError("Property not found.")
    except PropertyMetadata.DoesNotExist:
        raise ValueError("Property metadata not found.")
    except GuestArrivalInfo.DoesNotExist:
        raise ValueError("Guest arrival info not found.")
    except Location.DoesNotExist:
        raise ValueError("Location data not found.")
    
    # Convert the time fields to "HH:MM" format
    check_in_time = metadata.check_in_time.strftime("%H:%M") if metadata.check_in_time else None
    check_out_time = metadata.check_out_time.strftime("%H:%M") if metadata.check_out_time else None
     # Take the first 8 characters for hotelid and last 8 for chain id

    available_amenities = [
        {
            "Group": amenity.amenity.category,
            "name": amenity.amenity.name
        }
        for amenity in amenities if amenity.is_available
    ]
    close_out_time = None
    close_out_days = None
    property_rate = RatePlan.objects.filter(property=property_instance).first()
    if property_rate:
        close_out_days = property_rate.close_out_days
        close_out_time = property_rate.close_out_time.strftime("%H:%M") if property_rate.close_out_time else None
        

    return {
        "HotelDescriptiveContents": {
            "HotelDescriptiveContent": {
                "HotelName": property_instance.name,
                "HotelType": property_instance.property_type,
                "TimeZone": "Europe/Rome",
                "Plateform": "SU",
                "hotelid": property_instance.hotel_id,
                "ChainID": property_instance.chain_id,   
                "LanguageCode": "it",  
                "CurrencyCode": "EUR",
                "closeoutdays": close_out_days,
                "closeouttime": close_out_time,
                # "closeoutdays": metadata.close_out_days,
                # "closeouttime": metadata.close_out_time.strftime("%H:%M") if metadata.close_out_time else None,
                # "Taxes": {
                #     "Tax": [
                #         {
                #             "type": "Income Tax",
                #             "name": "Amount Received",
                #             "percent": 20
                #         } 
                #     ]
                # },
                "HotelDescriptiveContentNotifType": request_type,
                "PropertyLicenseNumber": metadata.regional_id_code,
                "OfficialCheckinTime": check_in_time,
                "OfficialCheckoutTime": check_out_time,
                "ContactInfos": {
                    "ContactInfo": [
                        {
                            "ContactProfileType": "PhysicalLocation",
                            "Addresses": {
                                "Address": {
                                    "AddressLine": location.street,
                                    "CityName": location.city,
                                    "PostalCode": location.post_code,
                                    "CountryName": "IT"
                                }
                            }
                        },
                        {
                            "ContactProfileType": "availability",
                            "Names": {
                                "Name": {
                                    "GivenName": arrival_info.contact_name,
                                    "Surname": arrival_info.contact_surname
                                }
                            },
                            "Addresses": {
                                "Address": {
                                    "AddressLine": location.street,
                                    "CityName": location.city,
                                    "PostalCode": location.post_code,
                                    "CountryName": "IT"
                                }
                            },
                            "NotificationEmail": arrival_info.email,
                            "Emails": {
                                "Email": [arrival_info.email]
                            },
                            "Phones": {
                                "Phone": [
                                    {
                                        "PhoneNumber": str(arrival_info.phone_number),
                                        "PhoneTechType": "5"
                                    }
                                ]
                            }
                        }
                    ]
                },
                "HotelInfo": {
                    "Position": {
                        "Latitude": location.latitude,
                        "Longitude": location.longitude
                    }
                },
                "Facilities": {
                    "Facility": available_amenities
                },
                "HotelDescription": property_instance.description
            }
        }
    }


def build_room_data(property_id, rooms) -> dict:
    """
    Builds data structure for rooms and their amenities in the SU API format.
    :param property_id: ID of the property for which room data will be uploaded.
    :return: JSON data structure required by SU API for room details.
    """
    # Fetch the property and rooms related to it
    try:
        property_instance = Property.objects.get(id=property_id)
    except Property.DoesNotExist:
        raise ValueError("Property not found.")
    except Room.DoesNotExist:
        raise ValueError("No active rooms found for this property.")

    # Format room data
    sellable_products = []
    for room in rooms:
        room_amenities = RoomAmenity.objects.filter(room=room, is_available=True)

        # Build amenities structure
        amenities_data = [
            {
                "Group": amenity.amenity.category,
                "name": amenity.amenity.name
            }
            for amenity in room_amenities
        ]

        if not room.is_onboarded:
            sellable_products.append({
            "InvStatusType": "Initial",
            "GuestRoom": {
                "Occupancy": {
                    "MaxOccupancy": room.max_occupancy,
                    "MaxChildOccupancy": room.max_child_occupancy
                },
                "Room": {
                    "roomid": str(room.id)[:8],
                    "RoomRate": str(room.room_rate),
                    "Quantity": str(room.quantity), 
                    "RoomType": room.get_room_type_display(),
                    "SizeMeasurement": str(room.size_measurement) if room.size_measurement else None,
                    "SizeMeasurementUnit": room.size_measurement_unit or None
                },
                "Facilities": {
                    "Facility": amenities_data
                },
                "Position": {
                    "Latitude": property_instance.location.latitude,
                    "Longitude": property_instance.location.longitude
                },
                "Address": {
                    "AddressLine": property_instance.location.street,
                    "CityName": property_instance.location.city,
                    "CountryName": "IT",
                    "PostalCode": property_instance.location.post_code
                },
                "Description": {
                    "Text": room.description or "",
                    "RoomDescription": room.description[:100] if room.description else "Room Details"
                }
            }
        })

    return {
        "SellableProducts": {
            "hotelid": property_instance.hotel_id,
            "SellableProduct": sellable_products
        }
    }


def build_room_update_data(room_instance, update_type):
    """
    Builds the data payload for updating room information on the SU API based on the type of update.
    
    Args:
        room_instance (Room): The room instance that needs to be updated.
        update_type (str): The type of update, e.g., "general_update", "is_active_update", "amenities_update".
    
    Returns:
        dict: The data payload formatted for the SU API.
    """

    hotel_id = room_instance.property.hotel_id
    room_id = str(room_instance.id)[:8]

    # Set InvStatusType based on update_type and is_active field
    if update_type == "room_delete":
        inv_status_type = "Delete"
    else:
        inv_status_type = "Active" if room_instance.is_active else "Deactivated"

    # Prepare data payload for is_active_update or room_delete
    if update_type in {'is_active_update', 'room_delete'}:
        data = {
            "SellableProducts": {
                "hotelid": hotel_id,
                "SellableProduct": [{
                    "InvNotifType": "Overlay",
                    "InvStatusType": inv_status_type,
                    "roomid": room_id
                }]
            }
        }
    else:
        # Data payload for general or amenities update
        data = {
            "SellableProducts": {
                "hotelid": hotel_id,
                "SellableProduct": [{
                    "InvNotifType": "Overlay",
                    "InvStatusType": "Modify",
                    "roomid": room_id,
                    "GuestRoom": {
                        "Room": {
                            "RoomRate": str(room_instance.room_rate),
                            "RoomType": room_instance.room_type,
                            "SizeMeasurement": str(room_instance.size_measurement),
                            "SizeMeasurementUnit": room_instance.size_measurement_unit
                        },
                        "Facilities": {
                            "Facility": []
                        },
                        "Description": {
                            "RoomDescription": room_instance.description,
                            "Text": room_instance.description
                        }
                    }
                }]
            }
        }

        # Add amenities if applicable
        if update_type in {"general_update", "amenities_update"}:
            amenities = RoomAmenity.objects.filter(room=room_instance, is_available=True)
            facilities_list = [
                {"Group": amenity.amenity.category, "name": amenity.amenity.name} for amenity in amenities
            ]
            data["SellableProducts"]["SellableProduct"][0]["GuestRoom"]["Facilities"]["Facility"] = facilities_list

    return data


def build_rate_plan_data(rate_instance, update_type):
    """
    Builds the data payload for updating rate plan information on the SU API based on the type of update.
    
    Args:
        rate_instance (RatePlan): The rate plan instance that needs to be updated.
        update_type (str): The type of update, e.g., "new", "modify", "activate", "deactivate", "delete".
    
    Returns:
        dict: The data payload formatted for the SU API.
    """
    
    hotel_id = rate_instance.property.hotel_id
    
    # Determine RatePlanNotifType based on update_type
    if update_type == "new":
        rate_plan_notif_type = "New"
    elif update_type == "modify":
        rate_plan_notif_type = "Overlay"
    elif update_type == "activate":
        rate_plan_notif_type = "Activate"
    elif update_type == "deactivate":
        rate_plan_notif_type = "Remove"
    elif update_type == "delete":
        rate_plan_notif_type = "Delete"
    else:
        raise ValidationError("Invalid update type provided.")

    # Base data payload structure
    data = {
        "RatePlans": {
            "hotelid": hotel_id,
            "RatePlan": [{
                "RatePlanNotifType": rate_plan_notif_type,
                "rateplanid": str(rate_instance.id),
            }]
        }
    }
    
    # Add additional fields if the rate plan is being created or modified
    if update_type in {"new", "modify"}:
        rate_plan_data = {
            "MealPlanID": str(rate_instance.meal_plan),
            "Description": {
                "Name": rate_instance.name,
                "Text": rate_instance.description
            }
        }

        # Add close-out fields if provided
        if rate_instance.close_out_days is not None:
            # Ensure close-out days is within the valid range
            if not (0 <= rate_instance.close_out_days <= 30):
                raise ValidationError("Close-out days must be between 0 and 30.")
            
            rate_plan_data["closeoutdays"] = str(rate_instance.close_out_days)
            
            # If close-out days is zero, ensure close-out time is provided and formatted correctly
            if rate_instance.close_out_days == 0:
                if rate_instance.close_out_time is None:
                    raise ValidationError("Close-out time is required if close-out days is set to 0.")
                rate_plan_data["closeouttime"] = rate_instance.close_out_time.strftime("%H:%M")

        # Include the rate plan data in the payload
        data["RatePlans"]["RatePlan"][0].update(rate_plan_data)

    return data

def build_room_rate_data(rate_instance):
    """
    Builds and formats data for a specific rate for onboarding or updating rates to SU API.

    :param rate_instance: RoomRate instance for a specific rate for a property.
    :param update_type: Determines if the request type is 'New' or 'Overlay' based on `is_onboarded` status.
    :return: Formatted dictionary for SU API rate plan request.
    """
    hotel_id = rate_instance.room.property.hotel_id
    room_id = str(rate_instance.room.id)[:8]
    rate_plan_id = str(rate_instance.rate_plan.id)

    # Calculate the date range
    start_date = rate_instance.start_date.strftime("%Y-%m-%d")
    end_date = rate_instance.end_date.strftime("%Y-%m-%d") if rate_instance.end_date else start_date

    # Build the complete rate plan data dictionary
    rate_plan_data = {
        "hotelid": hotel_id,
        "room": [{
            "roomid": room_id,
            "date": [{
                "from": start_date,
                "to": end_date,
                "rate": [{
                    "rateplanid": rate_plan_id
                }],
                "price": [
                    {
                        "NumberOfGuests": str(i),
                        "value": str(rate_instance.rate),
                    } for i in range(1, rate_instance.room.max_occupancy + 1)
                ],
                "closed": "0" if rate_instance.is_active else "1",
                "minimumstay": str(rate_instance.minimum_stay),
                "maximumstay": str(rate_instance.maximum_stay),
                "closedonarrival": "0",
                "closedondeparture": "0"
            }]
        }]
    }
    return rate_plan_data