from celery import shared_task
from django.utils.translation import gettext as _
from django.utils import timezone
from datetime import timedelta
from services.email import EmailService
import logging

logger = logging.getLogger(__name__)

@shared_task
def send_support_email(email, subject, message):
    """
    Task to send a support email.
    This task is executed asynchronously.
    """
    try:
        # Initialize email service
        email_service = EmailService()
        email_service.send_support_notification(email, subject, message)
    except Exception as e:
        logger.error(f"Failed to send support email: {str(e)}", exc_info=True)
        raise e
    return True

@shared_task
def send_admin_to_user_notification(message_id):
    """
    Task to send email notification to user when admin sends a message.
    This task is executed asynchronously after a 5-minute delay.
    """
    try:
        from .models import SupportMessage
        
        # Get the message
        try:
            message = SupportMessage.objects.get(id=message_id)
        except SupportMessage.DoesNotExist:
            logger.warning(f"SupportMessage with id {message_id} not found")
            return False
        
        # Check if message is still unread after 5 minutes
        if not message.is_read and message.is_from_support:
            email_service = EmailService()
            success = email_service.send_admin_message_notification(message)
            
            if success:
                logger.info(f"Admin message notification sent for message {message_id}")
            else:
                logger.error(f"Failed to send admin message notification for message {message_id}")
                
            return success
        else:
            logger.info(f"Message {message_id} already read or not from support, skipping notification")
            return True
            
    except Exception as e:
        logger.error(f"Failed to send admin message notification: {str(e)}", exc_info=True)
        raise e

@shared_task
def send_user_to_admin_notification(message_id):
    """
    Task to send instant email notification to admin when user sends a message.
    This task is executed asynchronously immediately.
    """
    try:
        from .models import SupportMessage
        
        # Get the message
        try:
            message = SupportMessage.objects.get(id=message_id)
        except SupportMessage.DoesNotExist:
            logger.warning(f"SupportMessage with id {message_id} not found")
            return False
        
        # Send notification if message is from user
        if not message.is_from_support:
            email_service = EmailService()
            success = email_service.send_user_message_notification(message)
            
            if success:
                logger.info(f"User message notification sent for message {message_id}")
            else:
                logger.error(f"Failed to send user message notification for message {message_id}")
                
            return success
        else:
            logger.info(f"Message {message_id} is from support, skipping user notification")
            return True
            
    except Exception as e:
        logger.error(f"Failed to send user message notification: {str(e)}", exc_info=True)
        raise e