from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.users.models import User
from apps.stay.models import Property, Location
from apps.integrations.models import PropertyOnlineCheckIn
import uuid
import json

class OnlineCheckInAPITestCase(TestCase):
    """Test case for the online check-in API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        
        # Create a test location
        self.location = Location.objects.create(
            street='Test Street',
            city='Test City',
            post_code='12345',
            country='IT',
            latitude=41.9028,
            longitude=12.4964
        )
        
        # Create a test property
        self.property = Property.objects.create(
            name='Test Property',
            property_type=Property.HOTEL,
            location=self.location
        )
        
        # Add the user as staff
        self.property.staffs.add(self.user)
        
        # Create a test online check-in configuration
        self.config = PropertyOnlineCheckIn.objects.create(
            property=self.property,
            is_enabled=True,
            istat_enabled=True,
            alloggati_enabled=False
        )
        
        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Define the API endpoints
        self.status_url = reverse('online-checkin-status', args=[self.property.id])
        self.config_url = reverse('online-checkin-config', args=[self.property.id])
        self.test_connection_url = reverse('online-checkin-test-connection', args=[self.property.id])
    
    def test_get_status(self):
        """Test getting the online check-in status"""
        response = self.client.get(self.status_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue('is_enabled' in response.data)
        self.assertTrue('istat_connected' in response.data)
        self.assertTrue('alloggati_connected' in response.data)
        self.assertEqual(response.data['is_enabled'], True)
    
    def test_get_config(self):
        """Test getting the online check-in configuration"""
        response = self.client.get(self.config_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue('is_enabled' in response.data)
        self.assertTrue('istat_enabled' in response.data)
        self.assertTrue('alloggati_enabled' in response.data)
        self.assertEqual(response.data['is_enabled'], True)
        self.assertEqual(response.data['istat_enabled'], True)
        self.assertEqual(response.data['alloggati_enabled'], False)
    
    def test_save_config(self):
        """Test saving the online check-in configuration"""
        data = {
            'is_enabled': True,
            'istat_enabled': False,
            'alloggati_enabled': True
        }
        
        response = self.client.post(
            self.config_url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['is_enabled'], True)
        self.assertEqual(response.data['istat_enabled'], False)
        self.assertEqual(response.data['alloggati_enabled'], True)
        
        # Verify the database was updated
        self.config.refresh_from_db()
        self.assertEqual(self.config.is_enabled, True)
        self.assertEqual(self.config.istat_enabled, False)
        self.assertEqual(self.config.alloggati_enabled, True)
    
    def test_save_config_validation(self):
        """Test validation when saving the online check-in configuration"""
        data = {
            'is_enabled': True,
            'istat_enabled': False,
            'alloggati_enabled': False
        }
        
        response = self.client.post(
            self.config_url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_test_connection(self):
        """Test the connection test endpoint"""
        data = {
            'connection_type': 'istat'
        }
        
        response = self.client.post(
            self.test_connection_url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue('success' in response.data)
        self.assertTrue('error' in response.data)
    
    def test_unauthorized_access(self):
        """Test unauthorized access to the endpoints"""
        # Create another user
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='otherpassword',
            name='Other User'
        )
        
        # Create another property
        other_property = Property.objects.create(
            name='Other Property',
            property_type=Property.HOTEL,
            location=self.location
        )
        
        # Add the other user as staff to the other property
        other_property.staffs.add(other_user)
        
        # Set up the API client with the other user
        self.client.force_authenticate(user=other_user)
        
        # Try to access the first property's endpoints
        response = self.client.get(self.status_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        response = self.client.get(self.config_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        response = self.client.post(
            self.test_connection_url,
            data=json.dumps({'connection_type': 'istat'}),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
