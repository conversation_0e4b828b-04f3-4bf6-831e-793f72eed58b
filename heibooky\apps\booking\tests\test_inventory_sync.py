import pytest
from django.test import TestCase
from django.utils import timezone
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from apps.booking.models import Booking, BookingBlock
from apps.booking.tasks.inventory import (
    sync_inventory_for_empty_dates,
    sync_single_property_inventory,
    _find_empty_dates
)
from apps.stay.models import Property, Room
from apps.pricing.models import RoomRate
from apps.users.models import User


class InventorySyncTestCase(TestCase):
    """Test cases for inventory synchronization functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a test property
        self.property = Property.objects.create(
            name="Test Hotel",
            hotel_id="TEST123",
            is_onboarded=True
        )
        
        # Create test rooms
        self.room1 = Room.objects.create(
            property=self.property,
            name="Room 1",
            quantity=5,
            is_active=True
        )
        
        self.room2 = Room.objects.create(
            property=self.property,
            name="Room 2",
            quantity=3,
            is_active=True
        )
        
        # Create test dates
        self.today = timezone.now().date()
        self.tomorrow = self.today + timedelta(days=1)
        self.next_week = self.today + timedelta(days=7)
        self.next_month = self.today + timedelta(days=30)

    def test_find_empty_dates_no_conflicts(self):
        """Test finding empty dates when there are no bookings, rates, or blocks."""
        start_date = self.today
        end_date = self.today + timedelta(days=5)
        
        empty_dates = _find_empty_dates(self.room1, start_date, end_date)
        
        # Should return all dates in the range
        expected_dates = []
        current = start_date
        while current <= end_date:
            expected_dates.append(current)
            current += timedelta(days=1)
        
        self.assertEqual(len(empty_dates), len(expected_dates))
        self.assertEqual(set(empty_dates), set(expected_dates))

    def test_find_empty_dates_with_booking(self):
        """Test finding empty dates when there are bookings."""
        # Create a booking for tomorrow
        booking = Booking.objects.create(
            property=self.property,
            checkin_date=timezone.make_aware(datetime.combine(self.tomorrow, datetime.min.time())),
            checkout_date=timezone.make_aware(datetime.combine(self.tomorrow + timedelta(days=2), datetime.min.time())),
            status=Booking.Status.CONFIRMED,
            is_manual=False
        )
        
        start_date = self.today
        end_date = self.today + timedelta(days=5)
        
        empty_dates = _find_empty_dates(self.room1, start_date, end_date)
        
        # Should exclude tomorrow and the day after (booking period)
        self.assertNotIn(self.tomorrow, empty_dates)
        self.assertNotIn(self.tomorrow + timedelta(days=1), empty_dates)
        self.assertIn(self.today, empty_dates)

    def test_find_empty_dates_with_room_rate(self):
        """Test finding empty dates when there are room rates."""
        # Create a room rate for next week
        rate = RoomRate.objects.create(
            room=self.room1,
            rate_plan_id=1,  # Assuming rate plan exists
            start_date=self.next_week,
            end_date=self.next_week + timedelta(days=3),
            price=100.00,
            is_active=True
        )
        
        start_date = self.next_week - timedelta(days=1)
        end_date = self.next_week + timedelta(days=5)
        
        empty_dates = _find_empty_dates(self.room1, start_date, end_date)
        
        # Should exclude dates covered by room rate
        rate_dates = [
            self.next_week,
            self.next_week + timedelta(days=1),
            self.next_week + timedelta(days=2),
            self.next_week + timedelta(days=3)
        ]
        
        for rate_date in rate_dates:
            self.assertNotIn(rate_date, empty_dates)

    def test_find_empty_dates_with_booking_block(self):
        """Test finding empty dates when there are booking blocks."""
        # Create a booking block
        block = BookingBlock.objects.create(
            property=self.property,
            start_date=self.next_week,
            end_date=self.next_week + timedelta(days=2),
            is_active=True
        )
        
        start_date = self.next_week - timedelta(days=1)
        end_date = self.next_week + timedelta(days=5)
        
        empty_dates = _find_empty_dates(self.room1, start_date, end_date)
        
        # Should exclude dates covered by booking block
        self.assertNotIn(self.next_week, empty_dates)
        self.assertNotIn(self.next_week + timedelta(days=1), empty_dates)
        self.assertNotIn(self.next_week + timedelta(days=2), empty_dates)

    @patch('apps.booking.tasks.inventory.update_inventory')
    def test_sync_single_property_inventory_success(self, mock_update_inventory):
        """Test successful single property inventory synchronization."""
        # Mock successful API response
        mock_update_inventory.return_value = {"Success": "Success", "TicketId": "123456"}
        
        result = sync_single_property_inventory(str(self.property.id), days_ahead=5)
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['processed_properties'], 1)
        self.assertGreater(result['total_rooms_processed'], 0)
        
        # Verify API was called
        self.assertTrue(mock_update_inventory.called)

    @patch('apps.booking.tasks.inventory.update_inventory')
    def test_sync_single_property_inventory_api_failure(self, mock_update_inventory):
        """Test single property inventory sync with API failure."""
        # Mock API failure response
        mock_update_inventory.return_value = {"Status": "Failed", "Error": "API Error"}
        
        result = sync_single_property_inventory(str(self.property.id), days_ahead=5)
        
        # Should still complete but with warnings
        self.assertIn(result['status'], ['success', 'partial_failure'])

    @patch('apps.booking.tasks.inventory.update_inventory')
    def test_sync_inventory_for_empty_dates_all_properties(self, mock_update_inventory):
        """Test inventory synchronization for all properties."""
        # Create another property
        property2 = Property.objects.create(
            name="Test Hotel 2",
            hotel_id="TEST456",
            is_onboarded=True
        )
        
        Room.objects.create(
            property=property2,
            name="Room 3",
            quantity=2,
            is_active=True
        )
        
        # Mock successful API response
        mock_update_inventory.return_value = {"Success": "Success", "TicketId": "123456"}
        
        result = sync_inventory_for_empty_dates(days_ahead=5)
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['total_properties'], 2)
        self.assertEqual(result['processed_properties'], 2)

    def test_sync_inventory_non_onboarded_property(self):
        """Test that non-onboarded properties are skipped."""
        # Create non-onboarded property
        non_onboarded_property = Property.objects.create(
            name="Non-onboarded Hotel",
            hotel_id="NON123",
            is_onboarded=False
        )
        
        result = sync_single_property_inventory(str(non_onboarded_property.id), days_ahead=5)
        
        self.assertEqual(result['status'], 'warning')
        self.assertIn('not onboarded', result['message'])

    def test_sync_inventory_property_not_found(self):
        """Test handling of non-existent property."""
        result = sync_single_property_inventory("non-existent-id", days_ahead=5)
        
        self.assertEqual(result['status'], 'warning')
        self.assertIn('not found', result['message'])

    @patch('apps.booking.tasks.inventory.update_inventory')
    def test_sync_inventory_with_mixed_dates(self, mock_update_inventory):
        """Test inventory sync with mix of occupied and empty dates."""
        # Create a booking for specific dates
        booking = Booking.objects.create(
            property=self.property,
            checkin_date=timezone.make_aware(datetime.combine(self.tomorrow, datetime.min.time())),
            checkout_date=timezone.make_aware(datetime.combine(self.tomorrow + timedelta(days=2), datetime.min.time())),
            status=Booking.Status.CONFIRMED,
            is_manual=False
        )
        
        # Create a room rate for different dates
        rate = RoomRate.objects.create(
            room=self.room1,
            rate_plan_id=1,
            start_date=self.tomorrow + timedelta(days=3),
            end_date=self.tomorrow + timedelta(days=5),
            price=100.00,
            is_active=True
        )
        
        # Mock successful API response
        mock_update_inventory.return_value = {"Success": "Success", "TicketId": "123456"}
        
        result = sync_single_property_inventory(str(self.property.id), days_ahead=10)
        
        self.assertEqual(result['status'], 'success')
        
        # Verify that API was called but not for all dates (some are occupied)
        call_args = mock_update_inventory.call_args_list
        if call_args:
            # Check that inventory data doesn't include occupied dates
            inventory_data = call_args[0][1]['inventory_data']
            occupied_date_strings = [
                self.tomorrow.strftime("%Y-%m-%d"),
                (self.tomorrow + timedelta(days=1)).strftime("%Y-%m-%d"),
                (self.tomorrow + timedelta(days=3)).strftime("%Y-%m-%d"),
                (self.tomorrow + timedelta(days=4)).strftime("%Y-%m-%d"),
                (self.tomorrow + timedelta(days=5)).strftime("%Y-%m-%d")
            ]
            
            synced_dates = [item['date'] for item in inventory_data]
            for occupied_date in occupied_date_strings:
                self.assertNotIn(occupied_date, synced_dates)

    def test_date_range_calculation(self):
        """Test that date ranges are calculated correctly."""
        days_ahead = 7
        result = sync_single_property_inventory(str(self.property.id), days_ahead=days_ahead)
        
        if 'date_range' in result:
            start_date = datetime.fromisoformat(result['date_range']['start_date']).date()
            end_date = datetime.fromisoformat(result['date_range']['end_date']).date()
            
            self.assertEqual(start_date, timezone.now().date())
            self.assertEqual(end_date, timezone.now().date() + timedelta(days=days_ahead))

    @patch('apps.booking.tasks.inventory.logger')
    def test_error_logging(self, mock_logger):
        """Test that errors are properly logged."""
        # Test with invalid property ID that will cause an error
        result = sync_single_property_inventory("invalid-uuid", days_ahead=5)
        
        # Should log the error
        self.assertTrue(mock_logger.warning.called or mock_logger.error.called)


@pytest.mark.django_db
class InventorySyncIntegrationTest(TestCase):
    """Integration tests for inventory synchronization."""

    def setUp(self):
        """Set up integration test data."""
        self.property = Property.objects.create(
            name="Integration Test Hotel",
            hotel_id="INT123",
            is_onboarded=True
        )
        
        self.room = Room.objects.create(
            property=self.property,
            name="Integration Room",
            quantity=10,
            is_active=True
        )

    @patch('services.su_api.su_api.send_request')
    def test_full_inventory_sync_workflow(self, mock_send_request):
        """Test the complete inventory synchronization workflow."""
        # Mock successful SU API response
        mock_send_request.return_value = {
            "Success": "Success",
            "TicketId": "789012"
        }
        
        # Run the sync task
        result = sync_single_property_inventory(str(self.property.id), days_ahead=7)
        
        # Verify successful completion
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['processed_properties'], 1)
        self.assertGreater(result['total_dates_synced'], 0)
        
        # Verify SU API was called
        self.assertTrue(mock_send_request.called)
        
        # Check the API call parameters
        call_args = mock_send_request.call_args
        self.assertEqual(call_args[0][0], "availability")  # API endpoint
        
        payload = call_args[0][1]
        self.assertEqual(payload['hotelid'], self.property.hotel_id)
        self.assertEqual(len(payload['room']), 1)
        self.assertEqual(payload['room'][0]['roomid'], str(self.room.id)[:8])
