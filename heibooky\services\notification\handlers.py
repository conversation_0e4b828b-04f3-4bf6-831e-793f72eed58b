
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone
from typing import Dict, Any, Optional, List, Union
from services.email import ReservationEmailService
from apps.integrations.models import Notification
import logging

logger = logging.getLogger(__name__)

class ReservationNotificationHandler:
    """Handler for reservation notifications across different channels (email, database, websocket)"""

    def __init__(self, property_obj, customer):
        self.property = property_obj
        self.customer = customer
        self.channel_layer = get_channel_layer()
        self.email_service = ReservationEmailService(property_obj, customer)

    def _send_websocket_notification(self, staff_member_id: int, title: str, message: str) -> None:
        """Send real-time WebSocket notification to staff member"""
        async_to_sync(self.channel_layer.group_send)(
            f"notifications_{staff_member_id}",
            {
                "type": "send_notification",
                "data": {
                    "title": title,
                    "message": message,
                    "time": timezone.now().isoformat()
                }
            }
        )

    def _create_db_notifications(self, title: str, message: str) -> None:
        """Create database notifications for all staff members"""
        for staff_member in self.property.staffs.all():
            Notification.create_notification(
                user=staff_member,
                title=title,
                message=message
            )

    def new_reservation_handler(self, booking) -> None:
        """Handle notifications for new reservations"""
        title = "Nuova Prenotazione"
        message = (f"Nuova prenotazione ricevuta per {self.property.name}. "
                          f"Cliente: {self.customer.first_name} {self.customer.last_name}, "
                          f"Check-in: {booking.checkin_date.date().strftime('%d/%m/%Y')}")

        # Send emails to all staff
        self.email_service.notify_new_reservation(booking.reservation_data)

        # Create database notifications
        self._create_db_notifications(title, message)

        # Send WebSocket notifications
        for staff_member in self.property.staffs.all():
            try:
                self._send_websocket_notification(staff_member.id, title, message)
            except Exception as e:
                logger.error(f"WebSocket notification failed for {staff_member.email}: {str(e)}")

    def modified_reservation_handler(self, booking, changes: Dict[str, Any]) -> None:
        """Handle notifications for modified reservations"""

        title = "Prenotazione Modificata"
        message = (f"Modifiche alla prenotazione per {self.property.name}. "
                  f"Cliente: {self.customer.first_name} {self.customer.last_name}. "
                  f"Visita la scheda della prenotazione per vedere maggiori dettagli")

        # Send emails to all staff
        self.email_service.notify_modification(booking.reservation_data, changes)

        # Create database notifications
        self._create_db_notifications(title, message)

        # Send WebSocket notifications
        for staff_member in self.property.staffs.all():
            try:
                self._send_websocket_notification(staff_member.id, title, message)
            except Exception as e:
                logger.error(f"WebSocket notification failed for {staff_member.email}: {str(e)}")

    def cancelled_reservation_handler(self, booking) -> None:
        """Handle notifications for cancelled reservations"""
        title = "Prenotazione Cancellata"
        message = (f"Cancellazione prenotazione per {self.property.name}. "
                  f"Cliente: {self.customer.first_name} {self.customer.last_name}, "
                  f"Data cancellazione: {timezone.now().strftime('%d/%m/%Y %H:%M')}")

        # Send emails to all staff
        self.email_service.notify_cancellation(booking.reservation_data)

        # Create database notifications
        self._create_db_notifications(title, message)

        # Send WebSocket notifications
        for staff_member in self.property.staffs.all():
            try:
                self._send_websocket_notification(staff_member.id, title, message)
            except Exception as e:
                logger.error(f"WebSocket notification failed for {staff_member.email}: {str(e)}")

class GeneralNotificationHandler:
    """
    Handler for general notifications across different channels (email, database, websocket).

    This class provides a unified interface for sending notifications to users through
    multiple channels, including WebSocket for real-time updates, database for persistence,
    and optionally email.

    Attributes:
        users: A single user or list of users to notify
        title: The notification title
        message: The notification message
        email_service: Optional email service to use for email notifications
        email_template: Optional template name for email notifications
        email_context: Optional additional context for email template
        channel_layer: The channel layer for WebSocket communications
    """
    def __init__(self,
                 users,
                 title: str,
                 message: str,
                 email_service=None,
                 email_template=None,
                 email_context=None):
        """
        Initialize the notification handler.

        Args:
            users: A single user object or a list of user objects
            title: The notification title
            message: The notification message
            email_service: Optional email service to use for email notifications
            email_template: Optional template name for email notifications
            email_context: Optional additional context for email template
        """
        self.users = users if isinstance(users, list) else [users]
        self.title = title
        self.message = message
        self.email_service = email_service
        self.email_template = email_template
        self.email_context = email_context or {}
        self.channel_layer = get_channel_layer()

    def _send_websocket_notification(self, user) -> None:
        """
        Send real-time WebSocket notification to a user.

        Args:
            user: The user to send the notification to
        """
        try:
            async_to_sync(self.channel_layer.group_send)(
                f"notifications_{user.id}",
                {
                    "type": "send_notification",
                    "data": {
                        "title": self.title,
                        "message": self.message,
                        "time": timezone.now().isoformat()
                    }
                }
            )
        except Exception as e:
            logger.error(f"WebSocket notification failed for user {user.id}: {str(e)}")

    def _create_db_notification(self, user) -> None:
        """
        Create database notification for a user.

        Args:
            user: The user to create the notification for
        """
        try:
            Notification.create_notification(
                user=user,
                title=self.title,
                message=self.message
            )
        except Exception as e:
            logger.error(f"Database notification failed for user {user.id}: {str(e)}")

    def _send_email_notification(self, user) -> None:
        """
        Send email notification to a user if email service is provided.

        Args:
            user: The user to send the email to
        """
        if not self.email_service or not self.email_template:
            return

        try:
            # Add user and notification data to context
            context = {
                'user': user,
                'title': self.title,
                'message': self.message,
                **self.email_context
            }

            # Create email content
            from services.email.email_service import EmailContent, EmailRecipient
            recipient = EmailRecipient(email=user.email, name=user.name)
            content = EmailContent(
                subject=self.title,
                template_name=self.email_template,
                context=context
            )

            # Send the email
            self.email_service.send_email(recipient, content)
        except Exception as e:
            logger.error(f"Email notification failed for user {user.id}: {str(e)}")

    def send_notification(self, channels=None) -> None:
        """
        Send notification across specified channels to all users.

        Args:
            channels: Optional list of channels to use.
                     Defaults to ['websocket', 'database'].
                     Possible values: 'websocket', 'database', 'email'
        """
        if channels is None:
            channels = ['websocket', 'database']

        for user in self.users:
            if 'websocket' in channels:
                self._send_websocket_notification(user)

            if 'database' in channels:
                self._create_db_notification(user)

            if 'email' in channels and self.email_service:
                self._send_email_notification(user)
