from django.contrib import admin
from django.utils.html import format_html
from .models import Chat, SupportMessage, MessageAttachment

class MessageAttachmentInline(admin.TabularInline):
    model = MessageAttachment
    readonly_fields = ('file_size', 'content_type', 'created_at')
    extra = 0

class SupportMessageInline(admin.TabularInline):
    model = SupportMessage
    readonly_fields = ('created_at', 'updated_at', 'is_from_support', 'message')
    fields = ('message', 'is_from_support', 'created_at', 'updated_at')
    extra = 0
    max_num = 10  # Limit to avoid loading too many messages
    can_delete = False
    ordering = ('-created_at',)

@admin.register(Chat)
class ChatAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'status', 'priority', 'created_at', 'last_message_at', 'message_count')
    list_filter = ('status', 'priority', 'created_at', 'last_message_at')
    search_fields = ('user__email', 'user__name')
    readonly_fields = ('created_at', 'updated_at', 'last_message_at', 'message_count')
    inlines = [SupportMessageInline]
    
    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User'
    
    def message_count(self, obj):
        count = obj.messages.count()
        return format_html(f'<span>{count} messages</span>')
    message_count.short_description = 'Messages'

@admin.register(SupportMessage)
class SupportMessageAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'is_from_support', 'is_read', 'message_preview', 'created_at', 'attachment_count')
    list_filter = ('is_from_support', 'is_read', 'created_at', 'chat__status')
    search_fields = ('message', 'chat__user__email')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [MessageAttachmentInline]
    
    def user_email(self, obj):
        return obj.chat.user.email
    user_email.short_description = 'User'
    
    def message_preview(self, obj):
        if len(obj.message) > 50:
            return f"{obj.message[:50]}..."
        return obj.message
    message_preview.short_description = 'Message'
    
    def attachment_count(self, obj):
        count = obj.attachments.count()
        return format_html(f'<span>{count} files</span>')
    attachment_count.short_description = 'Attachments'

@admin.register(MessageAttachment)
class MessageAttachmentAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'message_link', 'file_size_display', 'content_type', 'created_at')
    list_filter = ('content_type', 'created_at')
    search_fields = ('file_name', 'message__chat__user__email')
    readonly_fields = ('file_size', 'content_type', 'created_at')

    def file_size_display(self, obj):
        return f'{obj.file_size / 1024:.1f} KB'
    file_size_display.short_description = 'File Size'

    def message_link(self, obj):
        return format_html(f'<a href="/admin/support/supportmessage/{obj.message.id}">{obj.message}</a>')
    message_link.short_description = 'Related Message'
