#!/usr/bin/env python
"""
Test script to validate the room onboarding signal implementation.
This script tests if the signal correctly detects when is_onboarded changes 
from False to True and triggers the inventory setup task.
"""

import os
import sys
import django
from unittest.mock import patch, MagicMock

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

from apps.stay.models import Room, Property
from apps.booking.tasks.inventory import set_room_inventory_for_year
from django.contrib.auth import get_user_model

User = get_user_model()

def test_room_onboarding_signal():
    """Test that the signal triggers when room is_onboarded changes from False to True"""
    
    print("🧪 Testing room onboarding signal...")
      # Mock the Celery task to avoid actual execution
    with patch('apps.booking.tasks.inventory.set_room_inventory_for_year.delay') as mock_task:
        mock_task.return_value = MagicMock(id='test-task-id')
        
        try:
            # Find an existing room that is not onboarded
            room = Room.objects.filter(is_onboarded=False).first()
            
            if not room:
                # If no room with is_onboarded=False exists, temporarily use an onboarded room
                room = Room.objects.filter(is_onboarded=True).first()
                if not room:
                    print("❌ No room found to test with")
                    return False
                
                print("📋 No non-onboarded room found, temporarily setting one to False for testing")
                # Temporarily set it to False for testing
                room.is_onboarded = False
                room.save()
                print(f"📋 Set room {room.id} to is_onboarded=False for testing")
            
            print(f"📋 Testing with Room ID: {room.id}")
            print(f"📋 Initial is_onboarded status: {room.is_onboarded}")
            
            # Save the original state
            original_onboarded = room.is_onboarded
            
            # Change is_onboarded from False to True
            room.is_onboarded = True
            room.save()
            
            # Check if the task was called
            if mock_task.called:
                print("✅ Signal triggered successfully!")
                print(f"📞 Task called with arguments: {mock_task.call_args}")
                
                # Verify the task was called with the correct room ID
                called_args = mock_task.call_args[0]
                if called_args and str(room.id) == called_args[0]:
                    print("✅ Task called with correct room ID")
                    result = True
                else:
                    print(f"❌ Task called with wrong room ID. Expected: {room.id}, Got: {called_args}")
                    result = False
            else:
                print("❌ Signal was not triggered!")
                result = False
            
            # Restore original state
            room.is_onboarded = original_onboarded
            room.save()
            print(f"🔄 Restored room to original state: is_onboarded={original_onboarded}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error during test: {str(e)}")
            return False

def test_room_onboarding_no_trigger():
    """Test that the signal does NOT trigger when room is_onboarded goes from True to False or stays the same"""
    
    print("\n🧪 Testing that signal doesn't trigger for non-onboarding changes...")
    
    # Mock the Celery task
    with patch('apps.booking.tasks.inventory.set_room_inventory_for_year.delay') as mock_task:
        mock_task.return_value = MagicMock(id='test-task-id')
        
        try:
            # Find an existing room
            room = Room.objects.first()
            
            if not room:
                print("❌ No room found to test with")
                return False
            
            print(f"📋 Testing with Room ID: {room.id}")
            
            # Save the original state
            original_onboarded = room.is_onboarded
            original_description = room.description
            
            # Test 1: Change a different field (should not trigger inventory task)
            room.description = "Modified description for testing"
            room.save()
            
            # Test 2: Change is_onboarded from True to False (should not trigger inventory task)
            if room.is_onboarded:
                room.is_onboarded = False
                room.save()
            
            # Check if the task was called (it should NOT be called)
            if not mock_task.called:
                print("✅ Signal correctly did NOT trigger for non-onboarding changes")
                result = True
            else:
                print("❌ Signal incorrectly triggered for non-onboarding changes!")
                print(f"📞 Task called with arguments: {mock_task.call_args}")
                result = False
            
            # Restore original state
            room.is_onboarded = original_onboarded
            room.description = original_description
            room.save()
            print(f"🔄 Restored room to original state")
            
            return result
            
        except Exception as e:
            print(f"❌ Error during test: {str(e)}")
            return False

if __name__ == "__main__":
    print("🚀 Starting Room Onboarding Signal Tests")
    print("=" * 50)
    
    # Test 1: Signal triggers when room becomes onboarded
    test1_result = test_room_onboarding_signal()
    
    # Test 2: Signal doesn't trigger for other changes
    test2_result = test_room_onboarding_no_trigger()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"   Test 1 (Signal triggers on onboarding): {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Test 2 (Signal doesn't trigger unnecessarily): {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED! The signal implementation is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the signal implementation.")
        sys.exit(1)
