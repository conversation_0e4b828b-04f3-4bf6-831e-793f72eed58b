from allauth.account.adapter import De<PERSON>ultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.models import SocialApp
from django.core.exceptions import MultipleObjectsReturned

class CustomAccountAdapter(DefaultAccountAdapter):
    def save_user(self, request, user, form, commit=True):
        """
        Saves a new user instance using information provided by the form.
        """
        user = super().save_user(request, user, form, commit=False)
        user.email = user.email.lower()  # Normalize email address
        if commit:
            user.save()
        return user

class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):

    def get_app(self, request, provider, client_id=None):
        try:
            if client_id:
                return SocialApp.objects.get(provider=provider, client_id=client_id)
            return SocialApp.objects.get(provider=provider)
        except MultipleObjectsReturned:
            # Get the most recently created app
            return SocialApp.objects.filter(provider=provider).order_by('-id').first()
        except SocialApp.DoesNotExist:
            return super().get_app(request, provider, client_id)

    def populate_user(self, request, sociallogin, data):
        """
        Populates user instance with data from social provider.
        """
        user = super().populate_user(request, sociallogin, data)
        user.email = user.email.lower()  # Normalize email address
        
        # Set name from social account if available
        if 'name' in sociallogin.account.extra_data:
            user.name = sociallogin.account.extra_data['name']
        
        return user

    def save_user(self, request, sociallogin, form=None):
        """
        Saves a newly signed up social login user.
        """
        user = super().save_user(request, sociallogin, form)
        user.is_verified = True 
        user.save()
        return user
    