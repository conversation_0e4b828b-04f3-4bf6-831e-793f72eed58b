from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from rest_framework import status
from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Property
from apps.users.models import User
import uuid

class ManualBookingValidationTestCase(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        
        # Create a test property
        self.property = Property.objects.create(
            name='Test Property',
            property_type=Property.HOTEL,
            location_id=1  # Assuming a location with ID 1 exists
        )
        self.property.staffs.add(self.user)
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Set up dates for testing
        self.today = timezone.now().date()
        self.tomorrow = self.today + timedelta(days=1)
        self.day_after_tomorrow = self.today + timedelta(days=2)
        self.three_days_later = self.today + timedelta(days=3)
        self.four_days_later = self.today + timedelta(days=4)
        
        # Create a test customer
        self.customer = Customer.objects.create(
            first_name='<PERSON>',
            last_name='Doe',
            email='<EMAIL>',
            telephone='1234567890'
        )
        
        # Create a test reservation
        self.reservation = Reservation.objects.create(
            id='test_reservation',
            guest_name='John Doe',
            checkin_date=self.tomorrow,
            checkout_date=self.day_after_tomorrow,
            total_price=100.00
        )
        
    def test_create_manual_booking_with_existing_booking(self):
        """Test creating a manual booking that overlaps with an existing booking"""
        # Create an existing booking first
        existing_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.reservation,
            is_manual=True,
            checkin_date=self.tomorrow,
            checkout_date=self.day_after_tomorrow,
            status='new'
        )
        
        # Try to create a manual booking that overlaps with the existing booking
        url = '/booking/manual-booking/'
        data = {
            'property': self.property.id,
            'customer': {
                'first_name': 'Jane',
                'last_name': 'Smith',
                'email': '<EMAIL>',
                'telephone': '0987654321'
            },
            'reservation_data': {
                'checkin_date': self.tomorrow.isoformat(),
                'checkout_date': self.day_after_tomorrow.isoformat(),
                'total_price': 200.00
            }
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('overlap with an existing booking', str(response.data))
        
    def test_create_manual_booking_adjacent_to_existing_booking(self):
        """Test creating a manual booking that starts on the end date of an existing booking"""
        # Create an existing booking first
        existing_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.reservation,
            is_manual=True,
            checkin_date=self.tomorrow,
            checkout_date=self.day_after_tomorrow,
            status='new'
        )
        
        # Create a manual booking that starts on the end date of the existing booking
        url = '/booking/manual-booking/'
        data = {
            'property': self.property.id,
            'customer': {
                'first_name': 'Jane',
                'last_name': 'Smith',
                'email': '<EMAIL>',
                'telephone': '0987654321'
            },
            'reservation_data': {
                'checkin_date': self.day_after_tomorrow.isoformat(),
                'checkout_date': self.three_days_later.isoformat(),
                'total_price': 200.00
            }
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
    def test_create_manual_booking_ending_on_start_date_of_existing_booking(self):
        """Test creating a manual booking that ends on the start date of an existing booking"""
        # Create an existing booking first
        existing_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.reservation,
            is_manual=True,
            checkin_date=self.day_after_tomorrow,
            checkout_date=self.three_days_later,
            status='new'
        )
        
        # Create a manual booking that ends on the start date of the existing booking
        url = '/booking/manual-booking/'
        data = {
            'property': self.property.id,
            'customer': {
                'first_name': 'Jane',
                'last_name': 'Smith',
                'email': '<EMAIL>',
                'telephone': '0987654321'
            },
            'reservation_data': {
                'checkin_date': self.tomorrow.isoformat(),
                'checkout_date': self.day_after_tomorrow.isoformat(),
                'total_price': 200.00
            }
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
