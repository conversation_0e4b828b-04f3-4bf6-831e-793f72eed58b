services:
  web:
    build: .
    env_file:
      - .env
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    command: >
      sh -c "cd heibooky && 
      python manage.py migrate &&
      python manage.py collectstatic --noinput &&
      daphne -b 0.0.0.0 -p 8000 heibooky.asgi:application"
    volumes:
      - .:/app
      - log_volume:/var/log/heibooky
      - static_volume:/app/heibooky/staticfiles 
      - media_volume:/app/heibooky/media        
    expose:
      - 8000
    depends_on:
      db:
        condition: service_started
      redis:
        condition: service_started
    networks:
      - backend
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    environment:
      - DOCKER_CONTAINER=1
      - MAILGUN_API_KEY=${MAILGUN_API_KEY}
      - MAILGUN_DOMAIN=${MAILGUN_DOMAIN}

  celery-worker:
    build: .
    env_file:
      - .env
    command: >
      sh -c "cd heibooky &&
      celery -A heibooky worker 
      --loglevel=info
      --pool=solo
      --max-tasks-per-child=50"
    volumes:
      - .:/app
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    environment:
      - DOCKER_CONTAINER=1
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - C_FORCE_ROOT=false
      - DB_HOST=db
    user: "1000:1000" 
    depends_on:
      - redis
      - db
    networks:
      - backend
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery-beat:
    build: .
    env_file:
      - .env
    command: >
      sh -c "cd heibooky &&
      celery -A heibooky beat 
      --loglevel=info 
      --scheduler django_celery_beat.schedulers:DatabaseScheduler"
    volumes:
      - .:/app
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    environment:
      - DOCKER_CONTAINER=1
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - C_FORCE_ROOT=false
      - DB_HOST=db
    user: "1000:1000"
    depends_on:
      - redis
      - db
      - celery-worker
      - web
    networks:
      - backend
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    sysctls:
      - net.core.somaxconn=511
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - backend

  db:
    image: postgres:13
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    user: postgres
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - backend

  nginx:
    image: nginx:1.25-alpine
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro  # Read-only for config files
      - ./nginx/conf.d:/etc/nginx/conf.d:ro          # Read-only for config files
      - ./nginx/logs:/var/log/nginx
      - static_volume:/app/heibooky/staticfiles:ro    # Keep :ro for nginx
      - media_volume:/app/heibooky/media:ro          # Keep :ro for nginx
      - ./certbot/conf:/etc/letsencrypt:ro           # Read-only for certs
      - ./certbot/www:/var/www/certbot:ro            # Read-only for certbot
      - nginx_cache:/var/cache/nginx
      - nginx_run:/var/run
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - web
    networks:
      - backend
    restart: unless-stopped

  certbot:
    image: certbot/certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d backend.heibooky.com
    depends_on:
      - nginx

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:
  celery_logs:
  heibooky_logs:
  log_volume:
  nginx_cache:
  nginx_run:

networks:
  backend:
    driver: bridge