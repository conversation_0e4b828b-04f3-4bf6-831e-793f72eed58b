from rest_framework import status
from rest_framework.response import Response
from apps.support.models import SupportMessage
from django.db.models import Count, F, Subquery, OuterRef
from django.db.models.functions import TruncDay, TruncWeek
import datetime
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from django.views.decorators.cache import cache_page
from django.core.cache import cache
import logging
import pandas as pd
from apps.users.permissions import IsStaffPermission

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsStaffPermission])
@cache_page(60 * 5)  # Cache for 5 minutes
def support_analytics_summary(request):
    """
    Returns summary analytics for support team
    """
    cache_key = 'support_analytics_summary'
    cached_data = cache.get(cache_key)
    
    if cached_data:
        return Response(cached_data)
    
    try:
        # Get resolved chats (unique users with resolved status)
        resolved_chats = SupportMessage.objects.filter(
            status="resolved"
        ).values('user').distinct().count()
        
        # Calculate average response time
        # First, get all user messages
        user_messages = SupportMessage.objects.filter(
            is_from_support=False
        ).order_by('user', 'created_at')
        
        # For each user message, find the next staff reply
        response_times = []
        
        for msg in user_messages:
            # Find the next support message after this user message
            next_response = SupportMessage.objects.filter(
                user=msg.user,
                is_from_support=True,
                created_at__gt=msg.created_at
            ).order_by('created_at').first()
            
            if next_response:
                # Calculate time difference in minutes
                time_diff = (next_response.created_at - msg.created_at).total_seconds() / 60
                response_times.append(time_diff)
        
        # Calculate average response time
        avg_response_time = 0
        if response_times:
            avg_response_time = round(sum(response_times) / len(response_times))
        
        # Count active agents this week
        today = timezone.now()
        week_start = today - datetime.timedelta(days=today.weekday())
        active_agents = SupportMessage.objects.filter(
            is_from_support=True,
            created_at__gte=week_start
        ).values('user').distinct().count()
        
        # Count total messages sent
        total_messages = SupportMessage.objects.count()
        
        data = {
            "resolved_chats": resolved_chats,
            "average_response_time_minutes": avg_response_time,
            "active_agents_this_week": active_agents,
            "total_messages_sent": total_messages
        }
        
        # Cache the results
        cache.set(cache_key, data, 60 * 5)  # Cache for 5 minutes
        
        return Response(data)
        
    except Exception as e:
        logger.error(f"Error getting support analytics summary: {str(e)}", exc_info=True)
        return Response(
            {'error': 'Failed to retrieve support analytics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsStaffPermission])
@cache_page(60 * 5)  # Cache for 5 minutes
def support_analytics_timeseries(request):
    """
    Returns time series analytics for support team
    """
    range_param = request.query_params.get('range', 'daily')
    if range_param not in ['daily', 'weekly']:
        return Response(
            {'error': 'Range must be either "daily" or "weekly"'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    cache_key = f'support_analytics_timeseries_{range_param}'
    cached_data = cache.get(cache_key)
    
    if cached_data:
        return Response(cached_data)
    
    try:
        # Determine date truncation and range based on the parameter
        if range_param == 'daily':
            trunc_function = TruncDay('created_at')
            days_ago = 30  # Last 30 days for daily view
        else:  # weekly
            trunc_function = TruncWeek('created_at')
            days_ago = 90  # Last 90 days for weekly view
        
        # Start date for filtering
        start_date = timezone.now() - datetime.timedelta(days=days_ago)
        
        # Get counts of resolved chats per day/week
        resolved_data = SupportMessage.objects.filter(
            status='resolved',
            created_at__gte=start_date
        ).annotate(
            period=trunc_function
        ).values('period').annotate(
            count=Count('user', distinct=True)
        ).order_by('period')
        
        # Get counts of new chats (first message from each user) per day/week
        # First, get the earliest message from each user
        subquery = SupportMessage.objects.filter(
            user=OuterRef('user')
        ).order_by('created_at').values('created_at')[:1]
        
        first_messages = SupportMessage.objects.filter(
            created_at__gte=start_date
        ).annotate(
            first_message=Subquery(subquery)
        ).filter(
            created_at=F('first_message')
        )
        
        new_chats_data = first_messages.annotate(
            period=trunc_function
        ).values('period').annotate(
            count=Count('user', distinct=True)
        ).order_by('period')
        
        # Convert to dataframes for easier manipulation
        resolved_df = pd.DataFrame(list(resolved_data))
        new_chats_df = pd.DataFrame(list(new_chats_data))
        
        # Create a date range for all periods
        if range_param == 'daily':
            date_range = pd.date_range(start=start_date.date(), end=timezone.now().date())
            date_format = '%Y-%m-%d'
        else:
            # For weekly, create a range of week start dates
            week_start = start_date - datetime.timedelta(days=start_date.weekday())
            weeks = (timezone.now().date() - week_start.date()).days // 7 + 1
            date_range = [week_start + datetime.timedelta(weeks=i) for i in range(weeks)]
            date_format = '%Y-%m-%d'
        
        # Initialize result dictionaries
        labels = [d.strftime(date_format) for d in date_range]
        resolved_counts = [0] * len(labels)
        new_chat_counts = [0] * len(labels)
        
        # Fill in resolved counts
        if not resolved_df.empty and 'period' in resolved_df.columns:
            for _, row in resolved_df.iterrows():
                date_str = row['period'].strftime(date_format)
                if date_str in labels:
                    idx = labels.index(date_str)
                    resolved_counts[idx] = row['count']
        
        # Fill in new chat counts
        if not new_chats_df.empty and 'period' in new_chats_df.columns:
            for _, row in new_chats_df.iterrows():
                date_str = row['period'].strftime(date_format)
                if date_str in labels:
                    idx = labels.index(date_str)
                    new_chat_counts[idx] = row['count']
        
        data = {
            "labels": labels,
            "resolved_counts": resolved_counts,
            "new_chats": new_chat_counts
        }
        
        # Cache the results
        cache.set(cache_key, data, 60 * 5)  # Cache for 5 minutes
        
        return Response(data)
        
    except Exception as e:
        logger.error(f"Error getting support analytics timeseries: {str(e)}", exc_info=True)
        return Response(
            {'error': 'Failed to retrieve support analytics timeseries'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
