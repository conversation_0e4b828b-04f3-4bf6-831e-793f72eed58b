from apps.integrations.models import DownloadableTemplate
from apps.integrations.serializers import DownloadableTemplateSerializer
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

class DownloadableTemplateListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        templates = DownloadableTemplate.objects.filter(is_active=True)
        serializer = DownloadableTemplateSerializer(templates, many=True)
        return Response(serializer.data)