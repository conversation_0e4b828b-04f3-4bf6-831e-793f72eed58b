# Generated by Django 5.1.2 on 2025-04-13 19:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='teaminvite',
            name='is_registered',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='teaminvite',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='team_invites', to=settings.AUTH_USER_MODEL),
        ),
    ]
