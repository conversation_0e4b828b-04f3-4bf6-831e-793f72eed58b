from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import Booking, Reservation, Customer, BookingBlock

class BookingAdmin(admin.ModelAdmin):
    list_display = ('property', 'formatted_booking_date', 'status', 'is_manual', 'view_reservation_link',
                   'formatted_checkin_date', 'formatted_checkout_date')
    list_filter = ('property', 'status', 'channel_code', 'is_manual')
    search_fields = ('id', 'property__name', 'channel_code', 'customer__first_name', 'customer__last_name')
    readonly_fields = ('id', 'booking_date', 'checkin_date', 'checkout_date', 'payment_processed', 'is_manual',
                      'payment_processing_attempts', 'last_payment_attempt', 'reservation_link')

    fieldsets = (
        ('Booking Information', {
            'fields': ('id', 'property', 'customer', 'status', 'is_manual', 'channel_code')
        }),
        ('Dates', {
            'fields': ('booking_date', 'checkin_date', 'checkout_date'),
        }),
        ('Payment Information', {
            'fields': ('payment_processed', 'payment_processing_attempts', 'last_payment_attempt'),
        }),
        ('Related Information', {
            'fields': ('reservation_link',),
        })
    )

    def formatted_booking_date(self, obj):
        return obj.booking_date.strftime("%Y-%m-%d %H:%M")
    formatted_booking_date.short_description = "Booking Date"

    def formatted_checkin_date(self, obj):
        return obj.checkin_date.strftime("%Y-%m-%d")
    formatted_checkin_date.short_description = "Check-in"

    def formatted_checkout_date(self, obj):
        return obj.checkout_date.strftime("%Y-%m-%d")
    formatted_checkout_date.short_description = "Check-out"

    def view_reservation_link(self, obj):
        url = reverse('admin:booking_reservation_change', args=[obj.reservation_data.id])
        return format_html('<a href="{}">View Reservation</a>', url)
    view_reservation_link.short_description = "Reservation"

    def reservation_link(self, obj):
        url = reverse('admin:booking_reservation_change', args=[obj.reservation_data.id])
        return format_html('<a class="button" href="{}">Go to Reservation</a>', url)
    reservation_link.short_description = "Linked Reservation"


class ReservationAdmin(admin.ModelAdmin):
    list_display = ('id', 'guest_name', 'formatted_booked_at', 'formatted_checkin_date', 
                   'formatted_checkout_date', 'formatted_total_price', 'payment_type')
    list_filter = ('payment_type', 'booked_at')
    search_fields = ('id', 'guest_name', 'reservation_notif_id')
    readonly_fields = ('id', 'reservation_notif_id', 'booked_at', 'modified_at', 'processed_at',
                      'gross_price', 'total_price', 'total_tax', 'deposit', 'cancellation_fee',
                      'payment_due', 'commission_amount', 'extra_fees', 'taxes')

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'guest_name', 'reservation_notif_id')
        }),
        ('Dates', {
            'fields': ('booked_at', 'modified_at', 'processed_at', 'checkin_date', 'checkout_date')
        }),
        ('Financial Information', {
            'fields': ('gross_price', 'total_price', 'total_tax', 'deposit', 'cancellation_fee',
                      'payment_due', 'commission_amount', 'payment_type')
        }),
        ('Guest Information', {
            'fields': ('number_of_guests', 'number_of_adults', 'number_of_children', 'number_of_infants')
        }),
        ('Additional Information', {
            'fields': ('remarks', 'extra_fees', 'taxes', 'addons')
        })
    )

    def formatted_booked_at(self, obj):
        return obj.booked_at.strftime("%Y-%m-%d %H:%M") if obj.booked_at else "-"
    formatted_booked_at.short_description = "Booked At"

    def formatted_checkin_date(self, obj):
        return obj.checkin_date.strftime("%Y-%m-%d") if obj.checkin_date else "-"
    formatted_checkin_date.short_description = "Check-in"

    def formatted_checkout_date(self, obj):
        return obj.checkout_date.strftime("%Y-%m-%d") if obj.checkout_date else "-"
    formatted_checkout_date.short_description = "Check-out"

    def formatted_total_price(self, obj):
        return f"${obj.total_price:,.2f}"
    formatted_total_price.short_description = "Total Price"


class CustomerAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'email', 'telephone', 'city', 'country')
    search_fields = ('first_name', 'last_name', 'email', 'city', 'country')
    list_filter = ('country', 'state', 'city')
    readonly_fields = ('email', 'telephone', 'address')

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'email', 'telephone')
        }),
        ('Address Information', {
            'fields': ('address', 'city', 'state', 'country', 'zip_code')
        })
    )


class BookingBlockAdmin(admin.ModelAdmin):
    list_display = ('property', 'is_active', 'formatted_start_date', 'formatted_end_date', 'reason')
    list_filter = ('property', 'start_date', 'end_date')
    search_fields = ('property__name', 'reason')
    readonly_fields = ('is_active',)

    fieldsets = (
        ('Block Information', {
            'fields': ('property', 'is_active', 'start_date', 'end_date', 'reason')
        }),
    )

    def formatted_start_date(self, obj):
        return obj.start_date.strftime("%Y-%m-%d")
    formatted_start_date.short_description = "Start Date"

    def formatted_end_date(self, obj):
        return obj.end_date.strftime("%Y-%m-%d")
    formatted_end_date.short_description = "End Date"


admin.site.register(Booking, BookingAdmin)
admin.site.register(Reservation, ReservationAdmin)
admin.site.register(Customer, CustomerAdmin)
admin.site.register(BookingBlock, BookingBlockAdmin)