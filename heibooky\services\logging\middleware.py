import logging
import time

class RequestLogMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()
        logger = logging.getLogger('django')
        response = self.get_response(request)
        response_time = time.time() - start_time
        logger.info(
            "Processed request",
            extra={
                'request': request,
                'response_time': response_time,
                'response_code': response.status_code,
            }
        )
        return response