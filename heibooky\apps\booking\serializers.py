from rest_framework import serializers
from .models import Reservation, Booking, Customer, BookingBlock
from .utils import generate_unique_id
from django.db import IntegrityError
from django.utils import timezone
from apps.stay.utils import cleaning_staff_check
from django.db import transaction
import uuid
class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ['first_name', 'last_name', 'email', 'telephone', 'address', 'city', 'state', 'country', 'zip_code']

    def validate(self, data):
        required_fields = ['first_name', 'last_name', 'email', 'telephone']
        for field in required_fields:
            if not data.get(field):
                raise serializers.ValidationError({field: f"{field} is required."})
        return data

class ReservationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reservation
        fields = [
            'checkin_date', 'checkout_date', 'total_price', 'gross_price', 'total_tax', 'deposit',
            'cancellation_fee', 'processed_at', 'extra_fees', 'guest_name',
            'number_of_guests', 'taxes', 'payment_due', 'payment_type',
            'number_of_adults', 'number_of_infants', 'number_of_children',
            'commission_amount', 'booked_at', 'modified_at', 'remarks', 'addons'
        ]
        read_only_fields = ['id', 'booked_at', 'modified_at', 'processed_at', 'reservation_notif_id']

    def validate(self, data):
        required_fields = ['checkin_date', 'checkout_date', 'total_price']
        for field in required_fields:
            if not data.get(field):
                raise serializers.ValidationError({field: f"{field} is required."})

        if data['checkin_date'] >= data['checkout_date']:
            raise serializers.ValidationError("Check-out date must be after check-in date.")

        return data

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Get the request object from the context
        request = self.context.get('request')

        # Get property_id either from the instance or parent context
        property_id = None
        if hasattr(instance, 'booking') and hasattr(instance.booking, 'property_id'):
            property_id = instance.booking.property_id
        elif 'property_id' in self.context:
            property_id = self.context.get('property_id')

        # Check if this user is cleaning staff for this property
        if request and property_id and cleaning_staff_check(property_id, request.user):
            # Hide financial information for cleaning staff
            financial_fields = [
                'total_price', 'total_tax', 'deposit', 'cancellation_fee',
                'payment_due', 'commission_amount', 'gross_price'
            ]
            for field in financial_fields:
                if field in data:
                    data[field] = 0

            # Reset JSON fields
            data['extra_fees'] = {}
            data['taxes'] = {}

            # Empty addons dictionary for cleaning staff
            data['addons'] = {}

        return data


class BookingSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer()
    reservation_data = ReservationSerializer()

    class Meta:
        model = Booking
        fields = ['id', 'property', 'is_manual', 'status', 'customer', 'reservation_data']
        read_only_fields = ['id', 'is_manual']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure nested serializers have access to the context
        if 'context' in kwargs:
            self.fields['reservation_data'] = ReservationSerializer(context=self.context)
            self.fields['customer'] = CustomerSerializer(context=self.context)

    def create(self, validated_data):
        customer_data = validated_data.pop('customer')
        reservation_data = validated_data.pop('reservation_data')
        reservation_data['booked_at'] = timezone.now()

        try:
            # Start a transaction to ensure atomicity
            with transaction.atomic():
                # Check if customer already exists
                customer, created = Customer.objects.get_or_create(
                    email=customer_data['email'],
                    defaults=customer_data
                )

                # If the customer exists but wasn't created, consider updating their info
                if not created and any(customer_data.get(field) != getattr(customer, field)
                                      for field in customer_data):
                    # Update customer with the latest information
                    for field, value in customer_data.items():
                        setattr(customer, field, value)
                    customer.save()

                # Create Reservation record
                reservation = Reservation.objects.create(
                    id=generate_unique_id(Reservation),
                    guest_name=customer.get_full_name(),
                    gross_price=reservation_data.get('total_price', 0),
                    **reservation_data
                )

                # Create Booking record
                booking = Booking.objects.create(
                    customer=customer,
                    reservation_data=reservation,
                    checkin_date=reservation_data['checkin_date'],
                    checkout_date=reservation_data['checkout_date'],
                    status='new',
                    is_manual=True,
                    **validated_data
                )
                return booking

        except IntegrityError as e:
            raise serializers.ValidationError({"error": f"Database integrity error: {str(e)}"})
        except Exception as e:
            raise serializers.ValidationError({"error": f"An unexpected error occurred: {str(e)}"})

    def validate(self, data):
        property_obj = data['property']
        checkin_date = data['reservation_data']['checkin_date']
        checkout_date = data['reservation_data']['checkout_date']

        # Check if booking dates overlap with any blocked periods
        # A manual booking cannot be created on days with an existing block
        # But it can start from the end date of a booking block and end on the start date of a booking block
        booking_blocks = BookingBlock.objects.filter(
            property=property_obj,
            start_date__lt=checkout_date,  # Block starts before booking ends
            end_date__gt=checkin_date  # Block ends after booking starts
        )

        # Exclude blocks that end exactly on the booking's start date or start exactly on the booking's end date
        booking_blocks = booking_blocks.exclude(
            end_date=checkin_date  # Block ends on booking's start date (allowed)
        ).exclude(
            start_date=checkout_date  # Block starts on booking's end date (allowed)
        )

        if booking_blocks.exists():
            block = booking_blocks.first()
            raise serializers.ValidationError({
                "error": f"The selected booking dates overlap with a blocked period for this property "
                         f"(from {block.start_date} to {block.end_date}). "
                         f"A manual booking cannot overlap with existing blocks. "
                         f"Please choose alternative dates."
            })

        # Check if booking dates overlap with any existing bookings
        # A manual booking cannot be created on days with an existing booking
        # But it can start from the end date of an existing booking and end on the start date of an existing booking
        overlapping_bookings = Booking.objects.filter(
            property=property_obj,
            checkin_date__lt=checkout_date,  # Existing booking starts before new booking ends
            checkout_date__gt=checkin_date  # Existing booking ends after new booking starts
        )

        # Exclude bookings that end exactly on the new booking's start date or start exactly on the new booking's end date
        overlapping_bookings = overlapping_bookings.exclude(
            checkout_date=checkin_date  # Existing booking ends on new booking's start date (allowed)
        ).exclude(
            checkin_date=checkout_date  # Existing booking starts on new booking's end date (allowed)
        ).exclude(
            status__in=['cancelled', 'completed']  # Exclude bookings that are cancelled or completed
        )

        if overlapping_bookings.exists():
            booking = overlapping_bookings.first()
            raise serializers.ValidationError({
                "error": f"The selected booking dates overlap with an existing booking "
                         f"(from {booking.checkin_date} to {booking.checkout_date}). "
                         f"A manual booking cannot overlap with existing bookings. "
                         f"Please choose alternative dates."
            })

        # Ensure check-in date is today or a day in the future
        if checkin_date.date() < timezone.now().date():
            raise serializers.ValidationError("Check-in date must be today or a day in the future.")

        return data

    def to_representation(self, instance):
        """
        Pass property_id and request to the reservation serializer context
        to ensure financial data is properly filtered for cleaning staff
        """
        # Get the request from the context
        request = self.context.get('request')

        # Create a new context that includes both the request and property_id
        reservation_context = {
            'request': request,
            'property_id': instance.property_id
        }

        # Initialize the nested serializers with the appropriate context
        self.fields['reservation_data'] = ReservationSerializer(context=reservation_context)
        self.fields['customer'] = CustomerSerializer(context=self.context)

        # Call the parent class method to get the representation
        representation = super().to_representation(instance)

        # If user is cleaning staff, add a flag to indicate this is a restricted view
        if request and cleaning_staff_check(instance.property_id, request.user):
            representation['restricted_view'] = True

        return representation

class BookingBlockSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookingBlock
        fields = ['id', 'property', 'start_date', 'end_date', 'reason', 'is_active']
        read_only_fields = ['is_active']

    def validate(self, data):
        property_obj = data['property']
        start_date = data['start_date']
        end_date = data['end_date']

        # Basic date validation
        if end_date <= start_date:
            raise serializers.ValidationError({"end_date": "End date must be after start date."})

        if start_date < timezone.now().date():
            raise serializers.ValidationError({"start_date": "Start date must be today or in the future."})

        overlapping_bookings = Booking.objects.filter(
            property=property_obj,
            checkin_date__lt=end_date,  # Booking starts before block ends
            checkout_date__gt=start_date  # Booking ends after block starts
        )

        # Exclude bookings that start exactly on the block's end date or end exactly on the block's start date
        overlapping_bookings = overlapping_bookings.exclude(
            checkin_date=end_date  # Booking starts on block's end date (allowed)
        ).exclude(
            checkout_date=start_date  # Booking ends on block's start date (allowed)
        ).exclude(
            status__in=['cancelled', 'completed']  # Exclude bookings that are cancelled or completed
        )

        if overlapping_bookings.exists():
            booking = overlapping_bookings.first()
            raise serializers.ValidationError({
                "error": f"The selected block dates overlap with an existing booking "
                         f"(from {booking.checkin_date} to {booking.checkout_date}). "
                         f"A booking block cannot overlap with existing bookings. "
                         f"Please choose alternative dates."
            })

        return data

    def create(self, validated_data):
        # Check for overlapping block
        overlapping_block = BookingBlock.get_overlapping_block(
            validated_data['property'],
            validated_data['start_date'],
            validated_data['end_date']
        )

        if overlapping_block:
            # Update existing block with the widest date range
            overlapping_block.start_date = min(overlapping_block.start_date, validated_data['start_date'])
            overlapping_block.end_date = max(overlapping_block.end_date, validated_data['end_date'])
            if validated_data.get('reason'):
                overlapping_block.reason = validated_data['reason']
            overlapping_block.save()
            return overlapping_block

        # If no overlap, create new block
        return super().create(validated_data)


class BookingCancellationRequestSerializer(serializers.Serializer):
    """
    Serializer for handling booking cancellation requests from users.
    """
    booking_id = serializers.UUIDField(required=True, help_text="UUID of the booking to be cancelled")
    reason = serializers.CharField(required=True, help_text="Reason for cancellation request")

    def validate_booking_id(self, value):
        """
        Validate that the booking exists and belongs to the requesting user.
        """
        user = self.context.get('request').user

        try:
            booking = Booking.objects.get(id=value)

            # Check if the booking is already cancelled
            if booking.status == Booking.Status.CANCELLED:
                raise serializers.ValidationError("This booking is already cancelled.")

            # Store the booking in the context for later use
            self.context['booking'] = booking
            return value

        except Booking.DoesNotExist:
            raise serializers.ValidationError("Booking not found.")

    def validate_reason(self, value):
        """
        Validate the cancellation reason.
        """
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Please provide a more detailed reason for cancellation (minimum 10 characters).")
        return value