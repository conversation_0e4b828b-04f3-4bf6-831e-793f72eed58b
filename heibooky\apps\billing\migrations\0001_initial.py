# Generated by Django 5.1.2 on 2025-04-12 13:31

import django_countries.fields
import phonenumber_field.modelfields
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bill<PERSON><PERSON>ddress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('street_number', models.CharField(max_length=255)),
                ('postcode', models.CharField(max_length=10)),
                ('city', models.CharField(max_length=100)),
                ('country', django_countries.fields.CountryField(max_length=2)),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None, verbose_name='Phone Number')),
            ],
            options={
                'verbose_name_plural': 'Billing addresses',
            },
        ),
        migrations.CreateModel(
            name='BillingProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.CharField(max_length=255)),
                ('last_name', models.CharField(max_length=255)),
                ('date_of_birth', models.DateField()),
                ('nationality', django_countries.fields.CountryField(max_length=2)),
                ('gender', models.CharField(max_length=10)),
                ('recipient_type', models.CharField(choices=[('NP', 'Natural Person'), ('CO', 'Company')], default='NP', max_length=2)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('iban', models.CharField(max_length=34)),
                ('id_document', models.FileField(blank=True, null=True, upload_to='uploads/id_documents/')),
                ('company_document', models.FileField(blank=True, null=True, upload_to='uploads/company_documents/')),
            ],
        ),
        migrations.CreateModel(
            name='Taxation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('has_vat_number', models.BooleanField(default=False)),
                ('vat_number', models.CharField(blank=True, max_length=20, null=True)),
                ('tin_number', models.CharField(blank=True, max_length=20, null=True)),
                ('tin_country', django_countries.fields.CountryField(blank=True, max_length=2, null=True)),
                ('rent_more_than_4_properties', models.BooleanField(default=False)),
            ],
        ),
    ]
