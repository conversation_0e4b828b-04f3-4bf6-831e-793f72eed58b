from django.db.models.signals import pre_delete, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import RatePlan, RoomRate
from services.su_api import connect_rate_plan
from apps.integrations.utils import log_action, build_rate_plan_data
from apps.integrations.tasks.su_api import handle_rate_plans, handle_room_rates


@receiver(pre_delete, sender=RatePlan)
def delete_rate_plan(sender, instance, **kwargs):
    """
    Sends a delete request to SU API when a RatePlan instance is deleted.
    """
    if not instance.property.is_onboarded or not instance.is_onboarded:
        return
    
    try:
        rate_plan_data = build_rate_plan_data(instance, "delete")
        response = connect_rate_plan(rate_plan_data)
        log_action(
            user=instance.property.staffs.first(),
            property_id=instance.property.id,
            action="delete",
            description=f"Rate plan {instance.id} deleted on SU API.",
            status="successful" if response.get("Status") == "Success" else "failed",
            details={"response": response}
        )
    except Exception as e:
        log_action(
            user= instance.property.staffs.first(),
            property_id=instance.property.id,
            action="delete",
            description="Failed to delete rate plan on SU API.",
            status="failed",
            details={"error": str(e)}
        )


@receiver(post_save, sender=RatePlan)
def update_rate_plan(sender, instance, created, update_fields=None, **kwargs):
    """
    Handles updating, activating, or deactivating a rate plan based on changes to the `RatePlan` model.
    Sends overlay, activate, or deactivate requests to SU API.
    """
    # Skip if the rate plan is not onboarded or if the property is not onboarded
    if (update_fields and "is_onboarded" in update_fields) or not instance.property.is_onboarded:
        return
    
    # Determine action type based on created status or field changes
    action_type = "modify" if instance.is_onboarded else "new"
    
    # Check if `is_active` field was updated for activation/deactivation
    if update_fields and "is_active" in update_fields:
        notif_type = "activate" if instance.is_active else "deactivate"
    else:
        notif_type = action_type
    action = "create" if created else "update"
    user = instance.property.staffs.first()
    handle_rate_plans.delay(instance.property.id , user.id if user else None, action, notif_type)


@receiver(post_save, sender=RoomRate)
def send_room_rate_update(sender, instance, **kwargs):
    # Exclude updates on ticket_id, is_onboarded, or is_active fields
    if 'update_fields' in kwargs and kwargs['update_fields'] is not None and {'ticket_id', 'is_onboarded', 'is_active'}.intersection(kwargs['update_fields']):
        return

    action = "update" if instance.is_onboarded else "create"
    # Check if the property instance is onboarded
    user = instance.room.property.staffs.first()
    property_instance = instance.room.property
    if instance.room.property.is_onboarded and instance.room.is_onboarded:
        handle_room_rates.delay(property_instance.id, user.id if user else None, action)


@receiver(post_delete, sender=RoomRate)
def handle_room_rate_deletion(sender, instance: RoomRate, **kwargs):
    """
    Signal handler to trigger inventory synchronization when room rates are deleted.
    This ensures that previously covered dates get their base inventory restored.
    """
    from apps.booking.tasks.inventory import sync_single_property_inventory
    
    # Only process if the property is onboarded
    if not instance.room.property.is_onboarded:
        return
    
    # Schedule inventory sync for the affected property
    # Use a delay to ensure the deletion is committed first
    transaction.on_commit(
        lambda: sync_single_property_inventory.delay(
            str(instance.room.property.id),
            days_ahead=60  # Check 60 days ahead for rate deletions
        )
    )
