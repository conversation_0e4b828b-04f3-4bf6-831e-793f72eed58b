from django.urls import path
from apps.integrations.views import (
    OnlineCheckInStatusView,
    OnlineCheckInConfigView,
    TestConnectionView
)

urlpatterns = [
    # Status check endpoint
    path(
        'status/<uuid:property_id>/',
        OnlineCheckInStatusView.as_view(),
        name='online-checkin-status'
    ),
    
    # Configuration endpoints
    path(
        'config/<uuid:property_id>/',
        OnlineCheckInConfigView.as_view(),
        name='online-checkin-config'
    ),
    
    # Test connection endpoint
    path(
        'test-connection/<uuid:property_id>/',
        TestConnectionView.as_view(),
        name='online-checkin-test-connection'
    ),
]
