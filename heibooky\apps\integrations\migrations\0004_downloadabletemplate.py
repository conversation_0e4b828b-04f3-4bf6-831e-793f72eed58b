# Generated by Django 5.1.2 on 2025-05-14 18:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0003_propertyonlinecheckin'),
    ]

    operations = [
        migrations.CreateModel(
            name='DownloadableTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('file', models.FileField(upload_to='templates/')),
                ('category', models.CharField(choices=[('checkin', 'Check-in'), ('contract', 'Contract'), ('other', 'Other')], max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
