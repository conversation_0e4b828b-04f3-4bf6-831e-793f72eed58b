from rest_framework import serializers
from apps.integrations.models import PropertyOnlineCheckIn
from django.utils.translation import gettext_lazy as _

class OnlineCheckInStatusSerializer(serializers.Serializer):
    """Serializer for online check-in status endpoint"""
    is_enabled = serializers.BooleanField(read_only=True)
    istat_connected = serializers.BooleanField(read_only=True)
    alloggati_connected = serializers.BooleanField(read_only=True)
    last_sync = serializers.DateTimeField(read_only=True, allow_null=True)
    next_sync = serializers.DateTimeField(read_only=True, allow_null=True)


class OnlineCheckInConfigSerializer(serializers.ModelSerializer):
    """Serializer for online check-in configuration"""
    class Meta:
        model = PropertyOnlineCheckIn
        fields = ['is_enabled', 'istat_enabled', 'alloggati_enabled']
        read_only_fields = []

    def validate(self, data):
        """
        Validate that at least one reporting system is enabled if online check-in is enabled.
        """
        if data.get('is_enabled', False) and not (data.get('istat_enabled', False) or data.get('alloggati_enabled', False)):
            raise serializers.ValidationError(
                _("At least one reporting system (ISTAT or Alloggati Web) must be enabled when online check-in is enabled.")
            )
        return data


class TestConnectionRequestSerializer(serializers.Serializer):
    """Serializer for test connection request"""
    connection_type = serializers.ChoiceField(
        choices=['istat', 'alloggati'],
        required=True
    )


class TestConnectionResponseSerializer(serializers.Serializer):
    """Serializer for test connection response"""
    success = serializers.BooleanField()
    error = serializers.CharField(allow_null=True)


class ErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses"""
    status = serializers.CharField(default="error")
    error = serializers.DictField(
        child=serializers.CharField()
    )


class SuccessResponseSerializer(serializers.Serializer):
    """Serializer for success responses"""
    status = serializers.CharField(default="success")
    data = serializers.DictField(
        child=serializers.CharField()
    )
