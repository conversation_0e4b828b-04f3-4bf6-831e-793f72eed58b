import requests
from django.conf import settings
from requests.exceptions import HTTPError, RequestException
from typing import Any, Dict, Optional, List
import time
import random
from functools import wraps
from apps.integrations.utils import log_action
from typing import Optional, Dict, Any, Callable
from functools import wraps

def retry_with_backoff(func):
    """Retry decorator with exponential backoff and jitter."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Default retry settings - could be moved to Django settings
        max_tries = getattr(settings, 'SU_API_MAX_RETRIES', 3)
        initial_delay = getattr(settings, 'SU_API_INITIAL_DELAY', 1)
        max_delay = getattr(settings, 'SU_API_MAX_DELAY', 60)
        backoff = getattr(settings, 'SU_API_BACKOFF_FACTOR', 2)
        jitter = getattr(settings, 'SU_API_JITTER', 0.1)
        
        retry_exceptions = (HTTPError, RequestException, ConnectionError)
        tries = 0
        delay = initial_delay

        while tries < max_tries:
            try:
                return func(*args, **kwargs)
            except retry_exceptions as e:
                tries += 1
                if tries == max_tries:
                    raise e

                # Calculate delay with exponential backoff and jitter
                jitter_value = random.uniform(-jitter * delay, jitter * delay)
                actual_delay = min(delay + jitter_value, max_delay)
                
                time.sleep(actual_delay)
                delay *= backoff
        
        return func(*args, **kwargs)
    return wrapper

def log_su_api_response(endpoint: str):
    """
    Decorator to log SU API responses.
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user and property from args/kwargs if available
            user = kwargs.get('user', None)
            property_instance = kwargs.get('property', None)
            if property_instance:
                property_id = getattr(property_instance, 'id', None)
            else:
                property_id = kwargs.get('property_id', None)

            try:
                response = func(*args, **kwargs)
                
                # Log successful response
                if user and property_id:
                    log_action(
                        user=user,
                        property_id=property_id,
                        action='api_call',
                        description=f"SU API {endpoint} call successful",
                        status="successful",
                        details={"response": response, "endpoint": endpoint}
                    )
                return response

            except Exception as e:
                # Log error response
                if user and property_id:
                    log_action(
                        user=user,
                        property_id=property_id,
                        action='api_call',
                        description=f"SU API {endpoint} call failed",
                        status="failed",
                        details={"error": str(e), "endpoint": endpoint}
                    )
                raise
        return wrapper
    return decorator

def get_auth_header() -> Dict[str, str]:
    """
    Generates the Basic Auth header using credentials from settings.
    """
    return {
        'Authorization': f"Basic {settings.SU_API_KEY}",
        'app-id': settings.SU_API_APP_ID
    }

@retry_with_backoff
def send_request(
    endpoint: str, 
    payload: Optional[Dict] = None, 
    method: str = "POST",
    url: Optional[str] = None,
    headers: Optional[Dict] = None,
    user = None,
    property_id = None
) -> Dict[str, Any]:
    """
    Utility function to send requests to the SU API with retry logic and logging.
    """
    if not url:
        url = f"{settings.SU_API_BASE_URL}/{endpoint}"
    if not headers:
        headers = get_auth_header()

    try:
        response = requests.request(method, url, headers=headers, json=payload)
        #response.raise_for_status()
        response_data = response.json()
        
        # Log successful API call if user and property_id are provided
        if user and property_id:
            log_action(
                user=user,
                property_id=property_id,
                action='api_call',
                description=f"SU API {endpoint} call successful",
                status="successful",
                details={"response": response_data, "endpoint": endpoint}
            )
        return response_data

    except Exception as e:
        # Log failed API call if user and property_id are provided
        if user and property_id:
            log_action(
                user=user,
                property_id=property_id,
                action='api_call',
                description=f"SU API {endpoint} call failed",
                status="failed",
                details={"error": str(e), "endpoint": endpoint}
            )
        raise

@log_su_api_response("OTA_HotelDescriptiveContentNotif")
def onboard_property(property_data: Dict, user=None, property=None) -> Dict[str, Any]:
    """
    Sends property data to the SU API to onboard or update a property.
    """
    return send_request("OTA_HotelDescriptiveContentNotif", property_data, user=user, property_id=getattr(property, 'id', None))

def delete_property(hotel_id: str) -> Dict[str, Any]:
    """
    Sends a request to the SU API to delete a specific property listing.
    """
    payload = {"hotelid": hotel_id}
    return send_request("RemoveProperty", payload)

@log_su_api_response("OTA_HotelRoom")
def onboard_rooms(room_data: Dict, user=None, property=None) -> Dict[str, Any]:
    """
    Sends the formatted room data to the SU API.
    """
    return send_request("OTA_HotelRoom", room_data, user=user, property_id=getattr(property, 'id', None))

@log_su_api_response("imageCreate")
def upload_photos(property, photos: List, user=None) -> Dict[str, Any]:
    """
    Sends photos data to the SU API.
    """
    payload = {
        "hotelid": property.hotel_id,
        "images": [
            {
                "url": photo.image.url,
                "name": f"{property.name}-property-{photo.id}",
                "pms_imageid": photo.id,
            }
            for photo in photos
        ],
    }
    return send_request("imageCreate", payload, user=user, property_id=property.id)

@log_su_api_response("imageAssociation")
def associate_images(property: str, image_ids: List[str], user=None) -> Dict[str, Any]:
    """
    Associates images with a specific property.
    """
    payload = {
        "hotelid": property.hotel_id,
        "images": [{"pms_imageid": image_id} for image_id in image_ids],
    }
    return send_request("imageAssociation", payload, user=user, property_id=property.id)

@log_su_api_response("OTA_HotelRatePlan")
def connect_rate_plan(rate_data: Dict, user=None, property=None) -> Dict[str, Any]:
    """
    Creates or updates rate plans for a specific property.
    """
    return send_request("OTA_HotelRatePlan", rate_data, user=user, property_id=getattr(property, 'id', None))

@log_su_api_response("availability")
def associate_room_rate(rate_data: Dict, user=None, property=None) -> Dict[str, Any]:
    """
    Associates room rates (availability) with a specific property.
    """
    return send_request("availability", rate_data, user=user, property_id=getattr(property, 'id', None))

def reservations_pull(hotel_id: str) -> Dict[str, Any]:
    """
    Fetches reservation information for a property.
    """
    payload = {"hotelid": hotel_id}
    return send_request("Reservation", payload)

def confirm_or_cancel_booking(booking_id: str, status: Optional[str] = None) -> Dict[str, Any]:
    """
    Confirms or cancels bookings for a property.
    """
    url = 'https://connect-sandbox.su-api.com/SUAPI/service/requestbookings'
    payload = {"bookingid": booking_id, "status": status}
    return send_request("requestbookings", payload, url=url)

def list_bookings(hotel_id: str, start_date: str, end_date: str) -> Dict[str, Any]:
    """
    Lists bookings for a property within a specific time period.
    """
    payload = {
        "Su_hotelid": hotel_id,
        "date_from": start_date,
        "date_to": end_date,
    }
    return send_request("Bookings", payload)

def reservation_notification(property, reservation) -> Dict[str, Any]:
    """
    Sends a reservation notification to the SU API for acknowledgement of retrieval of reservations.
    """
    payload = {
        "hotelid": property.hotel_id,
        "reservation_notif": {
            "reservation_notif_id": [reservation.reservation_notif_id]
        }
    }
    return send_request("Reservation_notif", payload)

def reply_to_review(property_id: str, channel_id: str, review_id: str, reply_text: str, listing_id, location_id) -> Dict[str, Any]:
    """
    Sends a reply to a review through the SU API.
    
    Args:
        property_id: The property's SU API ID
        channel_id: The channel ID (e.g., "244" for Airbnb)
        review_id: The ID of the review being responded to
        reply_text: The text of the reply
    
    Returns:
        API response as dictionary
    """    
    payload = {
        "hotelid": property_id,
        "channelid": channel_id,
        "replytext": reply_text,
        "reviewid": review_id,
        "listingid" : listing_id,
        "locationid": location_id
    }
        
    return send_request("OTA_ReplyReview", payload)

@log_su_api_response("availability")
def update_inventory(property_instance, room_id, inventory_data, user=None) -> Dict[str, Any]:
    """
    Updates room inventory in the SU API.
    
    Args:
        property_instance: The property object
        room_id: ID of the room in SU system
        inventory_data: List of dictionaries with 'from', 'to', and 'roomstosell' fields
        user: Optional user for logging
    
    Returns:
        API response as dictionary
    """
    payload = {
        "hotelid": property_instance.hotel_id,
        "room": [
            {
                "roomid": room_id,
                "date": inventory_data
            }
        ]
    }
    
    return send_request(
        "availability", 
        payload, 
        user=user, 
        property_id=getattr(property_instance, 'id', None)
    )