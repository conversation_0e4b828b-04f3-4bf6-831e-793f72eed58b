# Generated by Django 5.1.2 on 2025-05-10 19:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0008_property_cover_image'),
    ]

    operations = [
        migrations.AddField(
            model_name='propertymetadata',
            name='is_setup_complete',
            field=models.BooleanField(default=False, verbose_name='Setup Complete'),
        ),
        migrations.AddField(
            model_name='propertymetadata',
            name='last_reminder_sent',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Last Reminder Sent'),
        ),
        migrations.AddField(
            model_name='propertymetadata',
            name='reminder_count',
            field=models.PositiveSmallIntegerField(default=0, verbose_name='Reminder Count'),
        ),
    ]
