from django.contrib import admin
from .models import RatePlan, RoomRate
    
class RoomRateInline(admin.TabularInline):
    model = RoomRate
    extra = 1
    fields = ('room', 'room_amount', 'start_date', 'end_date', 'minimum_stay', 'maximum_stay', 'is_active')
    show_change_link = True

@admin.register(RatePlan)
class RatePlanAdmin(admin.ModelAdmin):
    list_display = ('name', 'property_name', 'meal_plan_display', 'is_active', 'is_onboarded')
    list_filter = ('is_active', 'is_onboarded', 'meal_plan', 'property')
    search_fields = ('name', 'property__name', 'description')
    inlines = [RoomRateInline]
    save_on_top = True
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('property', 'name', 'description')
        }),
        ('Meal Plan', {
            'fields': ('meal_plan',)
        }),
        ('Booking Settings', {
            'fields': ('checkin_time', 'close_out_days', 'close_out_time')
        }),
        ('Status', {
            'fields': ('is_active', 'is_onboarded')
        }),
    )

    def property_name(self, obj):
        return obj.property.name
    property_name.short_description = 'Property'
    
    def meal_plan_display(self, obj):
        return dict(RatePlan.MEAL_CHOICES)[obj.meal_plan]
    meal_plan_display.short_description = 'Meal Plan'

@admin.register(RoomRate)
class RoomRateAdmin(admin.ModelAdmin):
    list_display = ('room', 'rate_plan_name', 'rate', 'room_amount', 'date_range', 'is_active', 'is_onboarded')
    list_filter = ('is_active', 'is_onboarded', 'rate_plan__property', 
                  'start_date', 'end_date')
    save_on_top = True
    
    fieldsets = (
        ('Rate Information', {
            'fields': ('rate_plan', 'room', 'rate', 'room_amount')
        }),
        ('Date Range', {
            'fields': (('start_date', 'end_date'),)
        }),
        ('Stay Restrictions', {
            'fields': (('minimum_stay', 'maximum_stay'),)
        }),
        ('Status', {
            'fields': ('is_active', 'is_onboarded', 'is_season', 'ticket_id')
        }),
    )

    def rate_plan_name(self, obj):
        return obj.rate_plan.name
    rate_plan_name.short_description = 'Rate Plan'
    
    def date_range(self, obj):
        end_date = obj.end_date or 'Ongoing'
        return f'{obj.start_date} to {end_date}'
    date_range.short_description = 'Date Range'
    
    def stay_limits(self, obj):
        return f'{obj.minimum_stay} - {obj.maximum_stay} nights'
    stay_limits.short_description = 'Stay Limits'