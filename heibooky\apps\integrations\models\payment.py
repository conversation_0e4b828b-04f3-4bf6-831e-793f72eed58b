from django.db import models
from django.conf import settings
from services.payment import DigitheraService
from django.utils.translation import gettext_lazy as _
import stripe
import uuid
import base64
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from django.db.models import F

stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeCustomer(models.Model):
    """
    Model representing a customer associated with a Stripe customer ID.
    """
    user = models.OneToOneField('users.User', on_delete=models.CASCADE, related_name="stripe_customer")
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.stripe_customer_id}"


class Payout(models.Model):
    PAYOUT_STATUS_CHOICES = [
        ('failed', 'Failed'),
        ('pending', 'Pending'),
        ('successful', 'Successful')
    ]
    """
    Model to record payouts made through Stripe.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, related_name="payouts")
    stripe_payment_intent_id = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default='eur')
    status = models.CharField(max_length=100, choices=PAYOUT_STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    invoice_status = models.BooleanField(default=False)
    booking = models.ForeignKey('booking.Booking', on_delete=models.SET_NULL, null=True, related_name='payouts')
    processing_errors = models.JSONField(default=dict, help_text="Log of processing errors")

    def __str__(self):
        return f"Payout {self.id} - Status: {self.status}"

class Sequence(models.Model):
    name = models.CharField(max_length=50, unique=True)
    value = models.IntegerField(default=0)
    
    def __str__(self):
        return f"{self.name} ({self.value})"

class Invoice(models.Model):
    """
    Model representing invoices linked to payouts.
    """
    SDI_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('failed', 'Failed'),
        ('uploaded', 'Uploaded')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payout = models.OneToOneField(Payout, on_delete=models.CASCADE, related_name="invoice")
    pdf_file = models.FileField(upload_to='invoices/%Y/%m/', null=True, blank=True)
    owner = models.ForeignKey('users.User', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    digithera_reference = models.CharField(max_length=255, blank=True)
    sdi_status = models.CharField(max_length=50, choices=SDI_STATUS_CHOICES, default='pending')
    progressive_number = models.CharField(max_length=10, unique=True, editable=False)

    def __str__(self):
        return f"Invoice {self.progressive_number}"

    def save(self, *args, **kwargs):
        if not self.progressive_number:
            current_year = timezone.now().year
            sequence = Sequence.objects.get_or_create(name=f'invoice_sequence_{current_year}')[0]
            with transaction.atomic():
                sequence.value = F('value') + 1
                sequence.save()
                new_value = Sequence.objects.get(pk=sequence.pk).value
                self.progressive_number = f"{current_year}{new_value:06d}"
        super().save(*args, **kwargs)

    def generate_digithera_invoice(self):
        """Generate and store invoice through Digithera API"""
        digithera = DigitheraService()
        
        # Construct payload from Payout data
        payload = {
            "vatCode": settings.COMPANY_VAT_CODE, 
            "fileName": f"INV_{self.id}.xml",
            "data": self._generate_invoice_data(),
            "customerData": str(self.payout.booking.customer.id) if self.payout.booking.customer else None
        }
        
        response = digithera.upload_invoice(payload)
        
        if response.get('resultCode') == 'SUCCESS':
            self.digithera_reference = response['resultData']['invoiceFileName']
            self.sdi_status = 'uploaded'
            self.save()
            return True
        return False

    def _generate_invoice_data(self):
        """Generate XMLPA compliant invoice"""
        from django.template.loader import render_to_string
        
        booking = self.payout.booking
        amount_decimal = Decimal(str(self.payout.amount))
        
        # Calculate amounts with proper rounding
        vat_rate = Decimal('0.22')
        taxable_amount = (amount_decimal / (Decimal('1') + vat_rate)).quantize(Decimal('0.00'))
        vat_amount = (taxable_amount * vat_rate).quantize(Decimal('0.00'))
        total_amount = (taxable_amount + vat_amount).quantize(Decimal('0.00'))

        context = {
            'payout': self.payout,
            'booking': booking,
            'COMPANY_NAME': settings.COMPANY_NAME,
            'COMPANY_VAT': settings.COMPANY_VAT_CODE,
            'taxable_amount': taxable_amount.quantize(Decimal('0.00')),
            'vat_amount': vat_amount.quantize(Decimal('0.00')),
            'total_amount': total_amount
        }
        
        xml_content = render_to_string('invoices/xmlpa_invoice.xml', context)
        
        return base64.b64encode(xml_content.encode()).decode()