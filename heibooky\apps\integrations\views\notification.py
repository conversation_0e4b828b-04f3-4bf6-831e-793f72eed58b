from rest_framework.views import APIView
from rest_framework.response import Response
from apps.integrations.models import Notification
from apps.integrations.serializers import NotificationSerializer
from rest_framework.permissions import IsAuthenticated
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.utils import timezone

class NotificationView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        notification_ids = request.data.get('notification_ids', [])
        if not notification_ids:
            return Response({"error": "No notification IDs provided"}, status=400)

        notifications = Notification.objects.filter(id__in=notification_ids, user=request.user)
        updated_count = notifications.update(is_read=True)

        return Response({"message": f"{updated_count} notifications marked as read"})

    def get(self, request):
        notifications = Notification.objects.filter(user=request.user).order_by("-created_at")
        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data)
    
class Test(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        title = request.data.get("title", "")
        message = request.data.get("message", "")
        
        if not title or not message:
            return Response({"error": "Title and message are required"}, status=400)

        # Create notification in the database
        notification = Notification.create_notification(user=request.user, title=title, message=message)

        # Send notification via WebSocket
        channel_layer = get_channel_layer()
        group_name = f"notifications_{request.user.id}"
        test_message = {
            "type": "send_notification",
            "data": {
                "title": title,
                "message": message,
                "time": timezone.now().isoformat()
            }
        }
        try:
            async_to_sync(channel_layer.group_send)(group_name, test_message)
            return Response({
                "message": "Notification sent successfully",
                "user_name": request.user.name,
                "data": test_message
            })
        except Exception as e:
            return Response({"error": str(e)}, status=500)
