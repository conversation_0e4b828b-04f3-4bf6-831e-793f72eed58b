import requests
import logging
from django.conf import settings
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class DigitheraService:
    BASE_URL = settings.DIGITHERA_BASE_URL 
    IDENTIFIER_CODE = settings.DIGITHERA_IDENTIFIER  
    PASSWORD = settings.DIGITHERA_API_TOKEN  

    def __init__(self):
        self.headers = {"Content-Type": "application/json"}
        self.auth_token = self.authenticate()
        if self.auth_token:
            self.headers["X-Auth-Token"] = self.auth_token
        else:
            raise Exception("Authentication failed. Check credentials or API configuration.")

    def authenticate(self):
        """Obtain X-Auth-Token via login endpoint"""
        login_url = f"{self.BASE_URL}/digithera_api/login"
        payload = {
            "identifierCode": self.IDENTIFIER_CODE,
            "password": self.PASSWORD
        }
        try:
            response = requests.post(
                login_url,
                json=payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            
            # Check if 'frontend' cookie exists (PDF section 2.3)
            auth_token = response.cookies.get("frontend")
            if auth_token:
                return auth_token
            
            # Fallback: Check if token is in response body (some implementations)
            json_response = response.json()
            if 'authToken' in json_response.get('resultData', {}):
                return json_response['resultData']['authToken']
            
            logger.error("Auth token not found in cookies or response body")
            return None
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Login HTTP error: {e.response.text}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Login request failed: {str(e)}")
        return None

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def upload_invoice(self, payload):
        """Send invoice to Digithera SDI system (PDF section 3.1)"""
        try:
            response = requests.post(
                f"{self.BASE_URL}/digithera_api/sdi/uploadInvoice",
                json=payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"Upload failed ({e.response.status_code}): {e.response.text}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Upload request failed: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
