# Generated by Django 5.1.2 on 2025-05-08 14:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0002_initial'),
        ('stay', '0008_property_cover_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='PropertyOnlineCheckIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_enabled', models.BooleanField(default=False, verbose_name='Online Check-in Enabled')),
                ('istat_enabled', models.BooleanField(default=False, verbose_name='ISTAT Reporting Enabled')),
                ('alloggati_enabled', models.BooleanField(default=False, verbose_name='Alloggati Web Reporting Enabled')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='Last Synchronization')),
                ('next_sync', models.DateTimeField(blank=True, null=True, verbose_name='Next Scheduled Synchronization')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('property', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='online_checkin', to='stay.property', verbose_name='Property')),
            ],
            options={
                'verbose_name': 'Property Online Check-in',
                'verbose_name_plural': 'Property Online Check-ins',
                'ordering': ['-updated_at'],
            },
        ),
    ]
