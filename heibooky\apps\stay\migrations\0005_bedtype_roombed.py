# Generated by Django 5.1.2 on 2025-04-29 14:14

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0004_remove_propertymetadata_national_id_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='BedType',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Bed Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='RoomBed',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('bed_count', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('bed_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.bedtype')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='beds', to='stay.room')),
            ],
            options={
                'verbose_name_plural': 'Room Beds',
                'unique_together': {('room', 'bed_type')},
            },
        ),
    ]
