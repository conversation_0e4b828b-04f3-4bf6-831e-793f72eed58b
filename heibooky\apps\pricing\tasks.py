from celery import shared_task
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import logging
from .models import RoomRate

logger = logging.getLogger(__name__)

@shared_task(name='apps.pricing.tasks.delete_expired_room_rates')
def delete_expired_room_rates():
    """
    Task to delete RoomRate objects that ended two months ago.
    This task is scheduled to run daily.
    """
    logger.info("Starting delete_expired_room_rates task")
    
    try:
        # Calculate the date two months ago
        two_months_ago = timezone.now().date() - timedelta(days=60)
        
        # Find all RoomRate objects with end_date before two months ago
        expired_rates = RoomRate.objects.filter(end_date__lt=two_months_ago)
        
        if not expired_rates.exists():
            logger.info("No expired room rates to delete")
            return {"status": "success", "message": "No expired room rates found"}
        
        # Log the number of rates to be deleted
        count = expired_rates.count()
        logger.info(f"Found {count} expired room rates to delete")
        
        # Delete the expired rates
        deletion_result = expired_rates.delete()
        
        logger.info(f"Successfully deleted {count} expired room rates")
        return {
            "status": "success", 
            "message": f"Deleted {count} expired room rates",
            "details": deletion_result
        }
        
    except Exception as e:
        logger.error(f"Error deleting expired room rates: {str(e)}")
        return {"status": "error", "message": str(e)}
