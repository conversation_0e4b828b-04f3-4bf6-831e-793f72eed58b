# Generated by Django 5.1.2 on 2025-04-12 13:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('booking', '0001_initial'),
        ('stay', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='booking',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='stay.property'),
        ),
        migrations.AddField(
            model_name='bookingblock',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='booking_blocks', to='stay.property'),
        ),
        migrations.AddField(
            model_name='booking',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='booking.customer'),
        ),
        migrations.AddField(
            model_name='booking',
            name='reservation_data',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='booking', to='booking.reservation'),
        ),
    ]
