# Generated by Django 5.1.2 on 2025-04-12 13:31

import apps.stay.models.property
import django.core.validators
import django.db.models.deletion
import phonenumber_field.modelfields
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Amenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('category', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name_plural': 'Amenities',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='GuestArrivalInfo',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('contact_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('contact_surname', models.Char<PERSON><PERSON>(max_length=255)),
                ('email', models.EmailField(max_length=254)),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(max_length=128, region=None)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=apps.stay.models.property.Photo.generate_unique_filename)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('is_onboarded', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='PropertyAmenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_available', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Property Amenities',
            },
        ),
        migrations.CreateModel(
            name='PropertyMetadata',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('check_in_time', models.TimeField(default='16:00', verbose_name='Check-in from')),
                ('check_out_time', models.TimeField(default='10:00', verbose_name='Check-out')),
                ('close_out_days', models.CharField(blank=True, max_length=2, null=True, verbose_name='Close-out days')),
                ('close_out_time', models.TimeField(blank=True, null=True, verbose_name='Close-out time')),
                ('cancelation_policy_type', models.CharField(choices=[('non_refundable', 'Non refundable'), ('partially_refundable', 'Partially refundable'), ('fully_refundable', 'Fully refundable')], default='non_refundable', max_length=20)),
                ('regional_id_code', models.CharField(blank=True, max_length=255, null=True, verbose_name='Regional Identification Code')),
                ('national_id_code', models.CharField(blank=True, max_length=255, null=True, verbose_name='National Identification Code')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Property Metadata',
            },
        ),
        migrations.CreateModel(
            name='PropertyOwnership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('relation_type', models.CharField(choices=[('owner', 'Legal owner'), ('tenant_admin', 'Tenant administrator'), ('intermediary_admin', 'Intermediary administrator')], max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='PropertyPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('property_config', 'Property Configuration'), ('booking_manage', 'Booking Management'), ('rates_manage', 'Rates Management'), ('room_config', 'Room Configuration'), ('view_only', 'View Only'), ('cleaning_staff', 'Cleaning Staff')], db_index=True, error_messages={'unique': 'This permission type already exists.'}, max_length=50, unique=True)),
                ('description', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('room_type', models.CharField(choices=[('1', 'Apartment'), ('4', 'Quadruple'), ('5', 'Suite'), ('7', 'Triple'), ('8', 'Twin'), ('9', 'Double'), ('10', 'Single'), ('12', 'Studio'), ('13', 'Family'), ('25', 'Dormitory room'), ('26', 'Bed in Dormitory'), ('27', 'Bungalow'), ('28', 'Chalet'), ('29', 'Holiday home'), ('31', 'Villa'), ('32', 'Mobile home'), ('33', 'Tent'), ('34', 'Powered/Unpowered Site'), ('35', 'King'), ('36', 'Queen')], max_length=2)),
                ('room_rate', models.DecimalField(decimal_places=2, max_digits=15)),
                ('max_occupancy', models.IntegerField()),
                ('max_child_occupancy', models.IntegerField()),
                ('quantity', models.IntegerField(default=1)),
                ('size_measurement', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('size_measurement_unit', models.CharField(blank=True, choices=[('sqm', 'Square Meters'), ('sqft', 'Square Feet')], max_length=4, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_onboarded', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='RoomAmenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_available', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Room amenities',
            },
        ),
        migrations.CreateModel(
            name='StaffRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TeamInvite',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('accepted', models.BooleanField(default=False)),
                ('accepted_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('street', models.CharField(max_length=255, verbose_name='Street Address')),
                ('post_code', models.CharField(max_length=10, verbose_name='Postal Code')),
                ('city', models.CharField(max_length=100, verbose_name='City')),
                ('country', models.CharField(max_length=100, verbose_name='Country')),
                ('latitude', models.FloatField(validators=[django.core.validators.MinValueValidator(-90), django.core.validators.MaxValueValidator(90)], verbose_name='Latitude')),
                ('longitude', models.FloatField(validators=[django.core.validators.MinValueValidator(-180), django.core.validators.MaxValueValidator(180)], verbose_name='Longitude')),
                ('is_editable', models.BooleanField(default=True, verbose_name='Is Editable')),
            ],
            options={
                'indexes': [models.Index(fields=['city', 'country'], name='stay_locati_city_ee2328_idx')],
            },
        ),
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('hotel_id', models.CharField(blank=True, editable=False, max_length=8, null=True)),
                ('chain_id', models.CharField(blank=True, editable=False, max_length=8, null=True)),
                ('name', models.CharField(max_length=255)),
                ('property_type', models.IntegerField(choices=[(1, 'Hotel'), (2, 'Motel'), (3, 'Vacational Rental')], default=1)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_multi_unit', models.BooleanField(default=False)),
                ('is_onboarded', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.location')),
            ],
            options={
                'verbose_name_plural': 'Properties',
            },
        ),
    ]
