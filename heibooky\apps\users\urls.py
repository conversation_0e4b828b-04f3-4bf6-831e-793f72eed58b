from django.urls import path
from .views import *
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView

urlpatterns = [
    path('signup/', SignupView.as_view(), name='signup'),
    path('team/signup/', TeamSignupView.as_view(), name='team-signup'),
    path('verify-email/', VerifyEmailView.as_view(), name='verify-email'),
    path('set-password/', SetPasswordView.as_view(), name='set-password'),
    path('login/', LoginView.as_view(), name='login'),
    path('staff/login/', SupportLoginView.as_view(), name='staff-login'), 
    path('change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('profile/', UserProfileView.as_view(), name='profile'),
    path('password-reset/', PasswordResetRequestView.as_view(), name='password-reset'),
    path('password-reset-confirm/', PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
    path('delete-account/', AccountDeleteView.as_view(), name='delete-account'),
    path('verify-token/', TokenVerifyView.as_view(), name='verify-token'),
    path('refresh-token/', TokenRefreshView.as_view(), name='refresh-token'),
]