from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.users.models import User
from apps.stay.models import Property, StaffRole, PropertyPermission
from apps.booking.models import Booking, Reservation, Customer
from django.utils import timezone
import uuid


class BookingAPITestCase(TestCase):
    def setUp(self):
        # Create test users
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            is_staff=True
        )

        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )

        self.cleaning_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )

        # Create test property
        self.property = Property.objects.create(
            name='Test Property',
            description='Test Description'
        )

        # Add users to property staff
        self.property.staffs.add(self.admin_user)
        self.property.staffs.add(self.regular_user)
        self.property.staffs.add(self.cleaning_user)

        # Create cleaning staff permission
        self.cleaning_permission = PropertyPermission.objects.create(
            name='cleaning_staff',
            description='Cleaning staff permission'
        )

        # Assign cleaning permission to cleaning user
        StaffRole.objects.create(
            property=self.property,
            user=self.cleaning_user,
            is_active=True
        ).permissions.add(self.cleaning_permission)

        # Create test customer
        self.customer = Customer.objects.create(
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>',
            telephone='1234567890'
        )

        # Create test reservation
        self.reservation = Reservation.objects.create(
            id=str(uuid.uuid4()),
            guest_name='Test Customer',
            checkin_date=timezone.now(),
            checkout_date=timezone.now() + timezone.timedelta(days=3),
            total_price=100.00,
            gross_price=120.00,
            total_tax=20.00,
            deposit=50.00,
            payment_due=50.00,
            commission_amount=10.00,
            addons={'breakfast': {'price': 15.00}}
        )

        # Create test booking
        self.booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.reservation,
            checkin_date=timezone.now().date(),
            checkout_date=(timezone.now() + timezone.timedelta(days=3)).date(),
            status='new',
            is_manual=True
        )

        # Set up API client
        self.client = APIClient()

    def test_admin_sees_financial_data(self):
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get(reverse('property-bookings-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that financial data is visible
        results = response.data.get('results', [])
        if results:
            booking_data = results[0]
            reservation_data = booking_data.get('reservation_data', {})
            self.assertNotEqual(reservation_data.get('total_price'), 0)
            self.assertNotEqual(reservation_data.get('gross_price'), 0)
            self.assertTrue(isinstance(reservation_data.get('addons'), dict))
            self.assertTrue(len(reservation_data.get('addons')) > 0)

    def test_cleaning_staff_cannot_see_financial_data(self):
        self.client.force_authenticate(user=self.cleaning_user)
        response = self.client.get(reverse('property-bookings-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that financial data is hidden
        results = response.data.get('results', [])
        if results:
            booking_data = results[0]
            self.assertTrue(booking_data.get('restricted_view', False))

            reservation_data = booking_data.get('reservation_data', {})
            self.assertEqual(reservation_data.get('total_price'), 0)
            self.assertEqual(reservation_data.get('gross_price'), 0)
            self.assertEqual(reservation_data.get('total_tax'), 0)
            self.assertEqual(reservation_data.get('deposit'), 0)
            self.assertEqual(reservation_data.get('payment_due'), 0)
            self.assertEqual(reservation_data.get('commission_amount'), 0)
            self.assertEqual(reservation_data.get('addons'), {})
            self.assertEqual(reservation_data.get('extra_fees'), {})
            self.assertEqual(reservation_data.get('taxes'), {})
