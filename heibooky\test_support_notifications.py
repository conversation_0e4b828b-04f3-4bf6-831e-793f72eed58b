#!/usr/bin/env python
"""
Test script for support message notifications
This script tests the email notification system for support messages
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\BackTrack\\heibooky')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

from apps.support.models import Chat, SupportMessage
from apps.users.models import User
from services.email.email_service import EmailService

def test_support_notifications():
    """Test the support notification system"""
    print("Testing Support Notification System")
    print("=" * 50)
    
    # Test 1: Check if models are properly configured
    print("\n1. Testing model configuration...")
    try:
        # Check if is_read field exists
        fields = [field.name for field in SupportMessage._meta.fields]
        if 'is_read' in fields:
            print("✓ is_read field found in SupportMessage model")
        else:
            print("✗ is_read field NOT found in SupportMessage model")
            return
            
        print("✓ Models are properly configured")
    except Exception as e:
        print(f"✗ Model configuration error: {e}")
        return
    
    # Test 2: Test email service methods
    print("\n2. Testing email service methods...")
    try:
        email_service = EmailService()
        
        # Check if new methods exist
        methods = ['send_admin_message_notification', 'send_user_message_notification']
        for method in methods:
            if hasattr(email_service, method):
                print(f"✓ {method} method found")
            else:
                print(f"✗ {method} method NOT found")
                
    except Exception as e:
        print(f"✗ Email service error: {e}")
        return
    
    # Test 3: Check if tasks are properly imported
    print("\n3. Testing task imports...")
    try:
        from apps.support.tasks import send_admin_to_user_notification, send_user_to_admin_notification
        print("✓ Tasks imported successfully")
    except ImportError as e:
        print(f"✗ Task import error: {e}")
        return
    
    # Test 4: Check template existence
    print("\n4. Testing email templates...")
    import os
    template_dir = 'c:\\Users\\<USER>\\BackTrack\\heibooky\\templates\\emails'
    templates = ['admin_message_notification.html', 'user_message_notification.html']
    
    for template in templates:
        template_path = os.path.join(template_dir, template)
        if os.path.exists(template_path):
            print(f"✓ {template} template found")
        else:
            print(f"✗ {template} template NOT found")
    
    print("\n" + "=" * 50)
    print("✓ Support notification system test completed!")
    print("\nNext steps:")
    print("1. Create a test user and chat")
    print("2. Send test messages")
    print("3. Verify email notifications are triggered")

if __name__ == "__main__":
    test_support_notifications()
