from django.contrib import admin
from .models import BillingProfile, BillingAddress, Taxation

class BillingAddressInline(admin.TabularInline):
    model = BillingAddress
    extra = 1
    max_num = 1

class TaxationInline(admin.TabularInline):
    model = Taxation
    extra = 1
    max_num = 1

@admin.register(BillingProfile)
class BillingProfileAdmin(admin.ModelAdmin):
    list_display = ['first_name', 'last_name', 'recipient_type', 'nationality', 'iban']
    search_fields = ['first_name', 'last_name', 'iban']
    inlines = [BillingAddressInline, TaxationInline]

# Register the individual models in the admin panel
@admin.register(BillingAddress)
class BillingAddressAdmin(admin.ModelAdmin):
    list_display = ['billing_profile', 'street_number', 'city', 'country']
    search_fields = ['billing_profile__first_name', 'billing_profile__last_name']

@admin.register(Taxation)
class TaxationAdmin(admin.ModelAdmin):
    list_display = ['billing_profile', 'has_vat_number', 'vat_number', 'rent_more_than_4_properties']
    search_fields = ['billing_profile__first_name', 'billing_profile__last_name', 'vat_number']

