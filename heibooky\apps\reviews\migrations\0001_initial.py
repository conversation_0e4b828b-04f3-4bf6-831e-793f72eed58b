# Generated by Django 5.1.2 on 2025-04-12 13:31

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('booking', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('external_review_id', models.CharField(help_text='Review ID from OTA', max_length=255, unique=True)),
                ('listing_id', models.CharField(blank=True, help_text='OTA Listing ID', max_length=255, null=True)),
                ('public_review', models.TextField(blank=True, null=True)),
                ('private_feedback', models.TextField(blank=True, null=True)),
                ('ratings', models.JSONField(blank=True, default=dict)),
                ('reviewer_id', models.Char<PERSON>ield(max_length=255)),
                ('reviewer_role', models.Char<PERSON><PERSON>(choices=[('host', 'Host'), ('guest', 'Guest')], max_length=10)),
                ('reviewee_id', models.Char<PERSON><PERSON>(max_length=255)),
                ('reviewee_role', models.CharField(choices=[('host', 'Host'), ('guest', 'Guest')], max_length=10)),
                ('channel_id', models.CharField(help_text='OTA Channel ID', max_length=10)),
                ('thread_id', models.CharField(blank=True, max_length=255, null=True)),
                ('is_hidden', models.BooleanField(default=False)),
                ('submitted_at', models.DateTimeField()),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('responses', models.JSONField(default=list, help_text='List of responses to the review')),
                ('booking', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='booking.booking')),
            ],
            options={
                'ordering': ['-submitted_at'],
            },
        ),
    ]
