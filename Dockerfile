# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    redis-tools \
    wget \
    && apt-get clean

# Create non-root user and group
RUN groupadd -g 1000 celery && \
    useradd -u 1000 -g celery -m -s /bin/bash celery

# Copy the requirements file
COPY requirements.txt .

# Upgrade pip and install dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create necessary directories and set permissions
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/run/celery \
    /var/log/celery && \
    chown -R celery:celery /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/run/celery \
    /var/log/celery && \
    chmod -R 755 /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs

# Create logging directory with correct permissions
RUN mkdir -p /var/log/heibooky && \
    touch /var/log/heibooky/django.log && \
    touch /var/log/heibooky/error.log && \
    touch /var/log/heibooky/celery.log && \
    chown -R celery:celery /var/log/heibooky && \
    chmod -R 755 /var/log/heibooky

# Copy the project code
COPY . .

# Final permission adjustments before running
RUN chown -R celery:celery /app && \
    chmod -R 755 /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV DOCKER_CONTAINER 1

# Expose port
EXPOSE 8000

# Switch to non-root user
USER celery

# Download GeoIP database and start application
CMD ["sh", "-c", "\
    cd heibooky && \
    python manage.py collectstatic --noinput --clear && \
    python manage.py migrate && \
    daphne -b 0.0.0.0 -p 8000 heibooky.asgi:application"]