# Generated by Django 5.1.2 on 2025-04-12 13:31

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_manual', models.BooleanField(default=False, help_text='True if the booking was created manually.')),
                ('channel_code', models.IntegerField(blank=True, choices=[(0, 'Booking Engine'), (1, 'STAAH'), (2, 'STAAH Max'), (9, 'Expedia'), (13, 'Bookit'), (19, 'Booking.com'), (38, 'Bookeasy'), (39, 'Needitnow'), (51, 'Not1night'), (55, 'Panpacific'), (57, 'Mitchellcorp'), (59, 'Travelocity XML'), (61, 'Hostelsclub'), (64, 'Li<PERSON>'), (65, 'Hotwire XML'), (66, 'Bedbank'), (69, 'Reconline'), (71, 'Sunhotels'), (76, 'Hostelworld'), (79, 'Jetstar'), (82, 'Hotelbeds New'), (85, 'Jactravel'), (87, 'HRS'), (90, 'Travel Republic'), (96, 'Hotelzon'), (97, 'Travelguru XML'), (103, 'Webrooms'), (104, 'GTA Travel'), (105, 'Goibibo'), (106, 'AOT'), (108, 'Tiket.com'), (109, 'Hotels Combined'), (110, 'Pitchup'), (116, 'Webhotelier'), (119, 'Klikhotel'), (122, 'Tablet Hotels'), (127, 'Prestigia'), (130, 'Hotusa'), (133, 'Hotel Network'), (140, 'Via XML'), (148, 'Hoterip'), (150, 'Ctrip'), (152, 'Pegipegi'), (153, 'Fastbooking'), (160, 'Tomas'), (162, 'Mr and Mrs Smith'), (165, 'Metglobal'), (170, 'Traveloka'), (176, 'Rakuten'), (177, 'Reservhotel'), (179, 'Flight Centre Wholesale'), (181, 'Despegar'), (183, 'Dorms'), (185, 'Booking Direct'), (186, 'Ostrovok'), (189, 'Agoda'), (203, 'Grabrooms'), (205, 'Travelanium'), (206, 'Tourplan'), (208, 'Bookingeye'), (213, 'Getaroom'), (217, 'Easemytrip'), (222, 'Sawadee XML'), (223, 'Travelclick'), (224, 'Hotel Bonanza'), (225, 'iChronoz'), (226, 'Booknpay XML'), (231, 'Simplebooking'), (232, 'DOTW'), (230, 'Thebuking'), (236, 'Odigeo'), (239, 'Silverdoor Apartments'), (242, 'iEscape'), (244, 'Airbnb Content'), (245, 'Travelstay Network XML'), (250, 'Speedybooker'), (251, 'EZ Travel'), (253, 'VRBO'), (255, 'TBO'), (266, 'OneFinerate'), (267, 'MG Holidays New'), (270, 'Saffron Stays'), (274, 'RezLive'), (275, 'Metachannel'), (280, 'Travco XML'), (281, 'Simplotel'), (282, 'IRCTC'), (284, 'HappyEasyGo'), (288, 'Situ'), (290, 'Apartments Online'), (291, 'Hyperguest'), (292, 'Tripjack'), (293, 'Bakuun'), (294, 'Pelican'), (295, 'Omnihotelier'), (296, 'Tripfactory'), (297, 'Hobse'), (299, 'Neorcha'), (300, 'Findbulous'), (301, 'Campingvision'), (302, 'Belhotel'), (303, 'Hottau'), (304, 'Hoo'), (305, 'Halalbooking'), (306, 'Jumpon.online'), (310, 'OffPeakLuxury'), (312, 'Travepic'), (313, 'Kliknbook'), (315, 'Robinhood'), (317, 'TXGB'), (318, 'Dedge'), (319, 'Altovita'), (320, 'Didatravel'), (321, '5pm'), (325, 'Brevistay'), (328, 'Airasia'), (329, 'Bag2bag'), (330, 'Aubergenie'), (336, 'Villafinder'), (339, 'Ctrip New'), (346, 'Roibos'), (347, 'Trustedstays'), (350, 'Busyrooms'), (351, 'Cleartrip'), (352, 'Travelplus'), (353, 'Klook'), (354, 'Thepercentage'), (355, 'Hotelsinone'), (356, 'Feratel'), (358, 'Seera'), (361, 'Bookmybooking'), (362, 'Spabreaks'), (364, 'Convergent'), (369, 'Hotelinx'), (370, 'Nuitee'), (371, 'Opengds'), (373, 'Codegen'), (374, 'Moteelz'), (377, 'Wowcher'), (378, 'Elong'), (381, 'Darent'), (382, 'Illusionsonline'), (5000, 'Max Channel'), (5001, 'Tripadvisor'), (5002, 'Google Hotel Ads'), (5003, 'UMI Digital'), (5004, 'Mybookingsite.io'), (5005, 'Instant'), (5006, 'Trivago')], help_text='Channel code of the booking.', null=True)),
                ('booking_date', models.DateTimeField(auto_now_add=True, help_text='Date and time when the booking was made.')),
                ('checkin_date', models.DateField(help_text='Check-in date for the booking.')),
                ('checkout_date', models.DateField(help_text='Check-out date for the booking.')),
                ('status', models.CharField(choices=[('new', 'New'), ('modified', 'Modified'), ('request', 'Requested'), ('cancelled', 'Cancelled'), ('completed', 'Completed')], default='new', help_text='Current status of the booking.', max_length=10)),
                ('payment_processed', models.BooleanField(default=False, help_text='Whether the owner payout has been processed')),
                ('payment_processing_attempts', models.IntegerField(default=0, help_text='Number of payment processing attempts')),
                ('last_payment_attempt', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='BookingBlock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(help_text='Start date of the block period.')),
                ('end_date', models.DateField(help_text='End date of the block period.')),
                ('reason', models.CharField(blank=True, help_text='Reason for blocking the booking period.', max_length=255)),
                ('is_active', models.BooleanField(default=False, help_text='Whether the block is active.')),
                ('task_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Celery task ID for reactivating rooms')),
            ],
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.CharField(max_length=50)),
                ('email', models.EmailField(max_length=254)),
                ('telephone', models.CharField(max_length=20)),
                ('address', models.TextField()),
                ('city', models.CharField(blank=True, max_length=50, null=True)),
                ('state', models.CharField(blank=True, max_length=50, null=True)),
                ('country', models.CharField(max_length=50)),
                ('zip_code', models.CharField(blank=True, max_length=10, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.CharField(help_text='Reservation ID from Su API.', max_length=100, primary_key=True, serialize=False)),
                ('guest_name', models.CharField(blank=True, help_text='Full name of the guest.', max_length=100)),
                ('booked_at', models.DateTimeField(blank=True, help_text='Date when the reservation was booked.', null=True)),
                ('modified_at', models.DateTimeField(blank=True, help_text='Last modification timestamp for the reservation.', null=True)),
                ('checkin_date', models.DateTimeField(blank=True, help_text='Expected arrival date', null=True)),
                ('checkout_date', models.DateTimeField(blank=True, help_text='Expected time of departure', null=True)),
                ('gross_price', models.DecimalField(blank=True, decimal_places=2, help_text='Gross price of the reservation.', max_digits=10, null=True)),
                ('total_price', models.DecimalField(decimal_places=2, help_text='Total price of the reservation after commission.', max_digits=10)),
                ('total_tax', models.DecimalField(blank=True, decimal_places=2, help_text='Total tax for the reservation.', max_digits=10, null=True)),
                ('deposit', models.DecimalField(decimal_places=2, help_text='Deposit amount.', max_digits=10)),
                ('cancellation_fee', models.DecimalField(blank=True, decimal_places=2, help_text='Cancellation fee amount.', max_digits=10, null=True)),
                ('processed_at', models.DateTimeField(blank=True, help_text='Timestamp when the booking was processed.', null=True)),
                ('reservation_notif_id', models.CharField(blank=True, help_text='Reservation notification ID.', max_length=100, null=True)),
                ('extra_fees', models.JSONField(default=dict, help_text='Extra fees in JSON format.')),
                ('taxes', models.JSONField(default=dict, help_text='Taxes in JSON format.')),
                ('payment_due', models.DecimalField(decimal_places=2, default=0.0, help_text='Total payment due.', max_digits=10)),
                ('payment_type', models.CharField(default='Hotel Collect', max_length=25)),
                ('commission_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Commission amount on bookings.', max_digits=10)),
                ('number_of_guests', models.IntegerField(default=1)),
                ('number_of_adults', models.IntegerField(default=1)),
                ('number_of_infants', models.IntegerField(default=0)),
                ('number_of_children', models.IntegerField(default=0)),
                ('remarks', models.CharField(blank=True, default=None, max_length=255, null=True)),
                ('addons', models.JSONField(blank=True, default=dict, help_text='Addons in JSON format.', null=True)),
            ],
        ),
    ]
