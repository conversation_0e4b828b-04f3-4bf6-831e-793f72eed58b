# Generated by Django 5.1.2 on 2025-04-12 13:31

import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='invoices/%Y/%m/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('digithera_reference', models.CharField(blank=True, max_length=255)),
                ('sdi_status', models.CharField(choices=[('pending', 'Pending'), ('failed', 'Failed'), ('uploaded', 'Uploaded')], default='pending', max_length=50)),
                ('progressive_number', models.CharField(editable=False, max_length=10, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Payout',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_payment_intent_id', models.CharField(max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='eur', max_length=10)),
                ('status', models.CharField(choices=[('failed', 'Failed'), ('pending', 'Pending'), ('successful', 'Successful')], default='pending', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('invoice_status', models.BooleanField(default=False)),
                ('processing_errors', models.JSONField(default=dict, help_text='Log of processing errors')),
            ],
        ),
        migrations.CreateModel(
            name='Sequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('value', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='StripeCustomer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_customer_id', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SUAPIActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_id', models.CharField(blank=True, help_text='ID of the property involved in the action', max_length=255, null=True)),
                ('action', models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('batch', 'Batch'), ('sync', 'Sync')], max_length=20)),
                ('description', models.TextField(help_text='Description of the action taken')),
                ('status', models.CharField(choices=[('successful', 'Successful'), ('failed', 'Failed'), ('partial', 'Partial')], max_length=10)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('details', models.JSONField(blank=True, help_text='Additional data related to the action, such as API response', null=True)),
            ],
            options={
                'verbose_name': 'SU API Action Log',
                'verbose_name_plural': 'SU API Action Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
