from urllib.parse import urlparse
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class AllowedOriginsValidator:
    """
    Custom validator that checks if the WebSocket connection's Origin header
    is in the list of allowed origins from CORS_ALLOWED_ORIGINS setting.
    """
    
    def __init__(self, application):
        self.application = application
        # Get allowed origins from settings
        self.allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
        logger.info(f"Initialized AllowedOriginsValidator with allowed origins: {self.allowed_origins}")
        
    async def __call__(self, scope, receive, send):
        if scope["type"] != "websocket":
            # Skip validation for non-websocket connections
            return await self.application(scope, receive, send)
            
        # Get the Origin header
        origin_header = None
        for name, value in scope.get("headers", []):
            if name == b"origin":
                origin_header = value.decode("latin1")
                break
                
        if not origin_header:
            logger.warning("WebSocket connection attempt with no Origin header")
            return await self.close_connection(send)
            
        # Parse the origin to get the scheme and netloc (host:port)
        parsed_origin = urlparse(origin_header)
        origin = f"{parsed_origin.scheme}://{parsed_origin.netloc}"
        
        # Check if the origin is allowed
        if origin in self.allowed_origins:
            logger.debug(f"Allowed WebSocket connection from origin: {origin}")
            return await self.application(scope, receive, send)
        else:
            logger.warning(f"Rejected WebSocket connection from origin: {origin}")
            return await self.close_connection(send)
            
    async def close_connection(self, send):
        """Close the WebSocket connection with a 403 status code."""
        await send({
            "type": "websocket.close",
            "code": 1008,  # Policy violation
        })
