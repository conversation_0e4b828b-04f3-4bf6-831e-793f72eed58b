from .models import BillingProfile  
import logging

logger = logging.getLogger(__name__)

def check_billing_complete(user):
    """
    Check if user has complete billing information
    Returns True if billing profile, address and taxation exist and are complete
    
    This optimized version uses proper exception handling and
    prefetch_related to reduce database queries.
    """
    try:
        # Check if user has billing profile
        try:
            billing_profile = user.billingprofile
        except AttributeError:
            return False
            
        # Check if billing profile has required fields
        if not billing_profile or not all([
            billing_profile.first_name,
            billing_profile.last_name,
            billing_profile.date_of_birth,
            billing_profile.nationality,
            billing_profile.gender,
            billing_profile.iban
        ]):
            return False
            
        # Check billing address
        try:
            billing_address = billing_profile.billing_address
        except BillingProfile.billing_address.RelatedObjectDoesNotExist:
            return False
            
        if not all([
            billing_address.street_number,
            billing_address.postcode,
            billing_address.city,
            billing_address.country
        ]):
            return False
            
        # Check taxation
        try:
            taxation = billing_profile.taxation
        except BillingProfile.taxation.RelatedObjectDoesNotExist:
            return False

        return True

    except Exception as e:
        logger.error(f"Error checking billing status: {str(e)}", exc_info=True)
        return False