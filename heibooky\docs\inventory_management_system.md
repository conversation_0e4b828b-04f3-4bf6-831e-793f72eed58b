# Inventory Management System

## Overview

The Inventory Management System provides a robust solution for managing property availability through the SU API. This system handles inventory control for room bookings, blocking periods, and ensures that inventory is properly synchronized across all channels.

**New Feature**: The system now includes comprehensive inventory synchronization for dates that don't have bookings, room rates, or booking blocks, ensuring complete inventory coverage.

## Key Components

### 1. SU API Integration

The system integrates with the SU API through the `update_inventory` function in `services/su_api/su_api.py`. This function allows for:

- Setting room inventory to a specific value
- Supporting date ranges for inventory control
- Ensuring proper authentication and error handling
- Logging all API interactions

```python
@log_su_api_response("invratecontrol")
def update_inventory(property_instance, room_id, inventory_data, user=None) -> Dict[str, Any]:
    """
    Updates room inventory in the SU API.
    """
    payload = {
        "hotelid": property_instance.hotel_id,
        "room": [
            {
                "roomid": room_id,
                "date": inventory_data
            }
        ]
    }
    
    url = f"{settings.SU_API_BASE_URL}/invratecontrol"
    
    return send_request(
        "invratecontrol", 
        payload, 
        url=url, 
        user=user, 
        property_id=getattr(property_instance, 'id', None)
    )
```

### 2. Celery Tasks for Inventory Management

Four primary Celery tasks handle inventory updates:

#### a. `block_rooms_task`

Sets inventory to zero for a booking block's date range, effectively making the property unavailable for booking.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def block_rooms_task(self, block_id: int) -> Dict[str, Any]:
    """Task to block rooms by setting inventory to zero for the specified date range."""
    # Implementation details
```

#### b. `unblock_rooms_task`

Restores inventory to original quantities when a booking block is removed.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def unblock_rooms_task(self, property_id: str, start_date, end_date) -> Dict[str, Any]:
    """Task to unblock rooms by resetting inventory to the original quantity."""
    # Implementation details
```

#### c. `update_inventory_for_booking`

Updates inventory when bookings are created, modified, or cancelled.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def update_inventory_for_booking(self, booking_id: str, action: str = "decrease") -> Dict[str, Any]:
    """
    Task to update inventory based on booking creation, modification, or cancellation.
    """
    # Implementation details
```

#### d. `sync_inventory_for_empty_dates` (NEW)

Synchronizes inventory for dates that don't have bookings, room rates, or booking blocks. This ensures that the SU API has correct inventory data for all available dates.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def sync_inventory_for_empty_dates(self, property_id: str = None, days_ahead: int = 90) -> Dict[str, Any]:
    """
    Task to synchronize inventory for dates that don't have bookings, room rates, or booking blocks.
    This ensures that the SU API has correct inventory data for all available dates.
    """
    # Implementation details
```

#### e. `sync_single_property_inventory` (NEW)

Synchronizes inventory for a single property's empty dates. Useful for on-demand synchronization when properties are updated.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def sync_single_property_inventory(self, property_id: str, days_ahead: int = 30) -> Dict[str, Any]:
    """
    Task to synchronize inventory for a single property's empty dates.
    """
    # Implementation details
```

### 3. Signal Handlers

The system uses Django signal handlers to automatically trigger inventory updates:

```python
@receiver(post_save, sender=BookingBlock)
def handle_booking_block_creation(sender, instance: BookingBlock, created: bool, **kwargs):
    """Handle booking block creation."""
    if created:
        transaction.on_commit(lambda: block_rooms_task.delay(instance.id))

@receiver(post_delete, sender=BookingBlock)
def handle_booking_block_deletion(sender, instance: BookingBlock, **kwargs):
    """Handle booking block deletion."""
    transaction.on_commit(lambda: unblock_rooms_task.delay(
        str(instance.property.id),
        instance.start_date,
        instance.end_date
    ))

@receiver(post_save, sender=Booking)
def handle_booking_inventory_update(sender, instance: Booking, created: bool, **kwargs):
    """Signal handler to update inventory when bookings are created, modified, or cancelled."""
    # Skip processing for manual bookings
    if instance.is_manual:
        return
        
    if created or instance.status in [Booking.Status.NEW, Booking.Status.MODIFIED]:
        # For new or modified bookings, decrease inventory
        transaction.on_commit(lambda: update_inventory_for_booking.delay(str(instance.id), "decrease"))
    elif instance.status == Booking.Status.CANCELLED:
        # For cancelled bookings, increase inventory
        transaction.on_commit(lambda: update_inventory_for_booking.delay(str(instance.id), "increase"))
```

**Enhanced Signal Handlers**:

- `handle_booking_block_deletion`: Now also triggers inventory sync after unblocking
- `handle_booking_inventory_update`: Enhanced to trigger sync after booking cancellations
- `handle_room_rate_deletion`: New signal to sync inventory when room rates are deleted

### 4. Scheduled Tasks

The system includes a scheduled task that runs daily to ensure inventory consistency:

```python
'sync-inventory-empty-dates': {
    'task': 'apps.booking.tasks.sync_inventory_for_empty_dates',
    'schedule': crontab(hour=2, minute=0),  # Run daily at 2:00 AM
    'options': {'expires': 7200},
},
```

### 5. Management Command

A comprehensive management command is available for manual inventory synchronization:

```bash
# Sync all properties
python manage.py sync_inventory

# Sync specific property
python manage.py sync_inventory --property <property_id>

# Sync with custom date range
python manage.py sync_inventory --days-ahead 60

# Run asynchronously
python manage.py sync_inventory --async

# List all onboarded properties
python manage.py sync_inventory --list-properties
```

### 6. Empty Date Detection Algorithm

The system uses an intelligent algorithm to identify dates that need inventory synchronization:

```python
def _find_empty_dates(room, start_date, end_date) -> List[datetime.date]:
    """
    Find dates between start_date and end_date that don't have:
    - Active bookings
    - Room rates
    - Booking blocks
    """
    # Implementation finds gaps in coverage
```

This algorithm:
- Identifies all dates in the specified range
- Excludes dates with active bookings (confirmed, new, modified)
- Excludes dates covered by room rates
- Excludes dates covered by booking blocks
- Returns only dates that need inventory updates

## Workflow

### Booking Block Creation

1. User creates a booking block through the API
2. The model's `post_save` signal triggers `block_rooms_task`
3. `block_rooms_task` sends inventory update to SU API setting it to 0
4. Notifications are sent to user about successful block creation
5. The property becomes unavailable for booking during the blocked period

### Booking Block Removal

1. User deactivates a booking block through the API
2. The API view triggers the deletion of the block
3. The model's `post_delete` signal triggers `unblock_rooms_task`
4. `unblock_rooms_task` sends inventory update to SU API restoring original quantities
5. **NEW**: `sync_single_property_inventory` runs to sync any inventory gaps
6. Notifications are sent to user about successful deactivation
7. The property becomes available for booking again

### Booking Creation/Modification

1. When a booking is created or modified, `handle_booking_inventory_update` signal handler runs
2. For new/modified bookings, it decreases inventory by 1
3. For cancelled bookings, it increases inventory by 1
4. **NEW**: For cancelled bookings, `sync_single_property_inventory` also runs to ensure proper coverage
5. All updates are processed asynchronously via Celery

### Room Rate Management

1. **NEW**: When room rates are deleted, `handle_room_rate_deletion` signal triggers
2. **NEW**: `sync_single_property_inventory` runs to restore base inventory for uncovered dates
3. This ensures no inventory gaps are left when pricing coverage changes

### Daily Inventory Synchronization

1. **NEW**: Daily at 2:00 AM, `sync_inventory_for_empty_dates` runs automatically
2. Processes all onboarded properties
3. Identifies dates without bookings, rates, or blocks
4. Updates SU API with base room quantities for those dates
5. Logs comprehensive results for monitoring

## Error Handling

The system implements robust error handling with:

- Automatic retries for failed API requests using exponential backoff
- Comprehensive logging for all operations
- Transaction management to ensure data consistency
- Exception handling to prevent cascading failures
- **NEW**: Detailed error reporting in sync results
- **NEW**: Property-level error isolation to prevent failures from affecting other properties

### Response Status Handling

The SU API returns responses with a success status that can be in different formats. The system properly checks for different success indicators:

```python
# Checking for both possible success response formats from SU API
is_successful = response.get("Success") == "Success" or response.get("Status") == "Success"

# Proper logging based on success/failure
if is_successful:
    logger.info(f"Successfully updated inventory: {response}")
else:
    logger.error(f"Failed to update inventory: {response}")
```

This ensures that successful API responses are correctly identified even when the API returns the status in different formats.

### New Error Handling Features

- **Graceful degradation**: Failed properties don't stop processing of other properties
- **Detailed error tracking**: Each failure is logged with specific error details
- **Retry mechanisms**: Failed sync operations can be retried manually or automatically
- **Monitoring support**: Results include metrics for monitoring and alerting

## Best Practices

- All inventory updates are performed asynchronously
- API calls are made only after database transactions are committed
- Events are logged for audit purposes
- Notifications are sent through a unified handler for consistency
- **NEW**: Regular scheduled synchronization ensures data consistency
- **NEW**: On-demand sync available for immediate corrections
- **NEW**: Comprehensive testing covers all sync scenarios

## Monitoring and Maintenance

### Key Metrics to Monitor

- Daily sync task completion rate
- Number of dates synchronized per property
- API response times and error rates
- Failed property count and reasons
- Inventory discrepancies detected and corrected

### Maintenance Commands

```bash
# Check sync status
python manage.py sync_inventory --list-properties

# Manual sync for troubleshooting
python manage.py sync_inventory --property <id> --days-ahead 30

# Async sync for large operations
python manage.py sync_inventory --async --days-ahead 90
```

### Logs to Monitor

- `celery.log`: Task execution and scheduling
- `django.log`: Application-level inventory operations
- `error.log`: Failed sync operations and API errors

## Performance Considerations

- Sync operations are batched by property to limit API load
- Configurable date ranges prevent excessive data processing
- Asynchronous execution prevents blocking of other operations
- Database queries are optimized with proper indexing and select_related
- Memory usage is controlled by processing properties individually
