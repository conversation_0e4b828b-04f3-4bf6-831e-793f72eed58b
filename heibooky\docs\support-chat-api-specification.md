# Support Chat System - API Specification

## Overview

This document provides detailed API specifications for the support chat system, including WebSocket events, REST endpoints, authentication requirements, and error handling.

## Authentication

### JWT Token Authentication
All API endpoints and WebSocket connections require valid JWT authentication tokens.

#### Token Requirements
- **Format**: Bearer token in Authorization header for REST APIs
- **WebSocket**: Token passed as query parameter `?token=your_jwt_token`
- **Expiration**: Tokens expire based on system configuration
- **Refresh**: Implement token refresh mechanism for long-lived connections

#### Permission Levels
- **Regular Users**: Access only their own chat sessions
- **Staff Users**: Access all chat sessions and admin features
- **Validation**: Server validates permissions on every request

## WebSocket API

### Connection Endpoint
```
ws://your-domain/ws/support/{chat_id}/?token={jwt_token}
```

### Connection Flow
1. Client establishes WebSocket connection with valid token
2. Server validates token and chat access permissions
3. <PERSON> adds client to chat group for real-time updates
4. Connection accepted or rejected with appropriate error codes

### Error Codes
- **4001**: No authentication token provided
- **4002**: Invalid authentication token
- **4003**: Token authentication error
- **4004**: No chat_id provided in URL
- **4005**: Chat not found
- **4006**: Access denied to chat
- **4007**: Error retrieving chat details

### Message Types

#### Incoming Messages (Client → Server)

##### 1. Chat Message
```json
{
  "type": "chat.message",
  "message": "Hello, I need help with my booking"
}
```
**Validation**:
- Message cannot be empty
- Maximum length: 5000 characters
- Rate limit: 30 messages per minute

##### 2. Typing Start
```json
{
  "type": "typing.start"
}
```
**Behavior**:
- Broadcasts typing indicator to other participants
- Auto-expires after 10 seconds
- Cached with user ID for state management

##### 3. Typing Stop
```json
{
  "type": "typing.stop"
}
```
**Behavior**:
- Clears typing indicator for the user
- Removes from cache
- Broadcasts stop event to participants

##### 4. File Upload Notification
```json
{
  "type": "file.upload.notify",
  "message_id": "uuid-of-message-with-attachment"
}
```
**Validation**:
- Message ID must exist
- User must have access to the message
- Message must belong to current chat

##### 5. Ping
```json
{
  "type": "ping"
}
```
**Response**: Server responds with `{"type": "pong"}`

#### Outgoing Messages (Server → Client)

##### 1. Chat Message
```json
{
  "type": "chat.message",
  "message": "Thank you for contacting support",
  "sender": "support|user",
  "timestamp": "2024-01-15T10:30:00Z",
  "id": "message-uuid",
  "status": "pending|in_progress|resolved|sent",
  "priority": "low|medium|high|urgent",
  "attachments": [
    {
      "id": "attachment-uuid",
      "file_name": "document.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "file_url": "/media/support_attachments/document.pdf"
    }
  ]
}
```

##### 2. Typing Indicator
```json
{
  "type": "typing.indicator",
  "action": "start|stop",
  "user_id": "user-uuid",
  "user_name": "John Doe",
  "is_support": true,
  "timestamp": "2024-01-15T10:30:00Z"
}
```
**Note**: Not sent to the user who triggered the typing event

##### 3. File Upload Confirmation
```json
{
  "type": "file.uploaded",
  "message_id": "message-uuid",
  "attachments": [...],
  "sender": "support|user",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

##### 4. Upload Progress
```json
{
  "type": "upload.progress",
  "message_id": "message-uuid",
  "progress": 75,
  "sender": "support|user"
}
```
**Note**: Only sent to the user uploading the file

##### 5. Error Response
```json
{
  "type": "error",
  "message": "Rate limit exceeded. Please slow down."
}
```

##### 6. Pong Response
```json
{
  "type": "pong"
}
```

## REST API Endpoints

### Chat Management

#### List Chats
```http
GET /support/chats/
Authorization: Bearer {jwt_token}
```

**Query Parameters**:
- `page`: Page number for pagination
- `page_size`: Number of items per page (max 100)
- `search`: Search by user email or name (staff only)
- `ordering`: Sort by `last_message_at`, `created_at`, `priority`, `status`

**Response**:
```json
{
  "count": 150,
  "next": "http://api.example.com/support/chats/?page=2",
  "previous": null,
  "results": [
    {
      "id": "chat-uuid",
      "user": {
        "id": "user-uuid",
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "status": "pending",
      "priority": "medium",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "last_message_at": "2024-01-15T10:30:00Z",
      "last_message": {
        "message": "Hello, I need help",
        "sender": "user",
        "created_at": "2024-01-15T10:30:00Z"
      }
    }
  ]
}
```

#### Get Chat Details
```http
GET /support/chats/{chat_id}/
Authorization: Bearer {jwt_token}
```

**Response**:
```json
{
  "id": "chat-uuid",
  "user": {
    "id": "user-uuid",
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "status": "in_progress",
  "priority": "high",
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "last_message_at": "2024-01-15T10:30:00Z"
}
```

#### Resolve Chat
```http
POST /support/chats/{chat_id}/resolve/
Authorization: Bearer {jwt_token}
```

**Response**:
```json
{
  "status": "resolved"
}
```

#### Set Chat Priority (Staff Only)
```http
POST /support/chats/{chat_id}/prioritize/
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "priority": "high"
}
```

**Response**:
```json
{
  "priority": "high"
}
```

### Message Management

#### List Messages
```http
GET /support/messages/?chat_id={chat_id}
Authorization: Bearer {jwt_token}
```

**Query Parameters**:
- `chat_id`: Required. UUID of the chat
- `page`: Page number for pagination
- `page_size`: Number of items per page (max 200)
- `ordering`: Sort by `created_at` (default: ascending)

**Response**:
```json
{
  "count": 25,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "message-uuid",
      "chat_id": "chat-uuid",
      "message": "Hello, I need help with my booking",
      "is_read": true,
      "created_at": "2024-01-15T10:30:00Z",
      "sender": "user",
      "user_name": "John Doe",
      "attachments": []
    }
  ]
}
```

#### Send Message
```http
POST /support/messages/
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data

chat_id: chat-uuid
message: Hello, how can I help you?
attachments: [file1, file2]  // Optional files
```

**Response**:
```json
{
  "id": "message-uuid",
  "chat_id": "chat-uuid",
  "message": "Hello, how can I help you?",
  "is_from_support": true,
  "is_read": false,
  "created_at": "2024-01-15T10:35:00Z",
  "updated_at": "2024-01-15T10:35:00Z",
  "sender": "support",
  "attachments": [
    {
      "id": "attachment-uuid",
      "file_name": "help_guide.pdf",
      "file_url": "/media/support_attachments/help_guide.pdf"
    }
  ]
}
```

#### Send Message to User (Staff Only)
```http
POST /support/messages/send_to_user/
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data

user_id: user-uuid
message: We've received your inquiry and will respond shortly
attachments: [file1, file2]  // Optional files
```

#### Track Upload Progress
```http
POST /support/messages/upload_progress/
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "message_id": "message-uuid",
  "progress": 75
}
```

**Response**:
```json
{
  "status": "progress_sent"
}
```

## File Upload Specifications

### Supported File Types
- **Documents**: PDF, DOC, DOCX, TXT
- **Images**: JPG, JPEG, PNG
- **MIME Types**: Validated on server side

### File Size Limits
- **Maximum Size**: 5MB per file
- **Multiple Files**: Supported in single message
- **Validation**: Client and server-side validation required

### Upload Process
1. Client selects files and validates locally
2. Client sends message with attachments via REST API
3. Server validates and stores files
4. Server creates message with attachment records
5. Client notifies WebSocket of upload completion
6. Server broadcasts file upload notification

### Security Measures
- File type validation by extension and MIME type
- Virus scanning (recommended)
- Secure file storage with access controls
- File URL generation with authentication

## Rate Limiting

### WebSocket Rate Limits
- **Messages**: 30 per minute per user
- **Typing Events**: Debounced to prevent spam
- **File Uploads**: 5 per minute per user

### REST API Rate Limits
- **General Endpoints**: 100 requests per minute per user
- **File Upload**: 10 requests per minute per user
- **Staff Endpoints**: Higher limits for administrative functions

### Rate Limit Headers
```http
X-RateLimit-Limit: 30
X-RateLimit-Remaining: 25
X-RateLimit-Reset: 1642248000
```

## Error Handling

### HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (invalid token)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **413**: Payload Too Large (file size exceeded)
- **429**: Too Many Requests (rate limited)
- **500**: Internal Server Error

### Error Response Format
```json
{
  "error": "Validation failed",
  "details": {
    "message": ["This field cannot be empty"],
    "attachments": ["File size exceeds 5MB limit"]
  }
}
```

### WebSocket Error Handling
- Connection errors result in specific close codes
- Runtime errors sent as error type messages
- Automatic reconnection recommended for clients
- Graceful degradation for offline scenarios

## Performance Considerations

### Database Optimization
- Indexed queries for chat and message retrieval
- Efficient pagination with cursor-based pagination for large datasets
- Connection pooling for database connections
- Query optimization for message history

### Caching Strategy
- Redis cache for typing indicators
- Rate limiting counters in cache
- Session data caching
- File metadata caching

### Scalability
- Horizontal scaling support with Redis channel layer
- Load balancing for WebSocket connections
- CDN integration for file serving
- Database read replicas for message history

## Monitoring and Logging

### Metrics to Track
- WebSocket connection counts
- Message throughput
- File upload success rates
- Error rates by type
- Response times

### Logging Requirements
- All WebSocket connections and disconnections
- Message sending and receiving events
- File upload activities
- Error conditions and exceptions
- Security-related events (failed authentication, etc.)

### Health Checks
- WebSocket endpoint availability
- Database connectivity
- File storage accessibility
- Cache system status
