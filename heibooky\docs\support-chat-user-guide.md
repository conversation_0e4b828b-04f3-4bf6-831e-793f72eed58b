# Support Chat System - User Implementation Guide

## Overview

This guide provides comprehensive instructions for implementing the frontend interface for regular users to interact with the support chat system.

## Authentication & Authorization

### WebSocket Connection
- **Endpoint**: `ws://your-domain/ws/support/{chat_id}/`
- **Authentication**: JWT token passed as query parameter
- **Example**: `ws://localhost:8000/ws/support/123e4567-e89b-12d3-a456-************/?token=your_jwt_token`

### User Permissions
- Users can only access their own chat sessions
- Chat ID must belong to the authenticated user
- Invalid access attempts will result in connection closure

## WebSocket Event Types

### Incoming Events (From Server)

#### 1. Chat Message
```json
{
  "type": "chat.message",
  "message": "Hello! Thank you for contacting support.",
  "sender": "support",
  "timestamp": "2024-01-15T10:30:00Z",
  "id": "msg-uuid",
  "status": "in_progress",
  "priority": "medium",
  "attachments": []
}
```

#### 2. Typing Indicator
```json
{
  "type": "typing.indicator",
  "action": "start|stop",
  "user_id": "support-user-uuid",
  "user_name": "Support Agent",
  "is_support": true,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 3. File Upload Confirmation
```json
{
  "type": "file.uploaded",
  "message_id": "msg-uuid",
  "attachments": [
    {
      "id": "att-uuid",
      "file_name": "screenshot.png",
      "file_size": 256000,
      "content_type": "image/png",
      "file_url": "/media/support_attachments/screenshot.png"
    }
  ],
  "sender": "user",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 4. Upload Progress (Only for uploader)
```json
{
  "type": "upload.progress",
  "message_id": "msg-uuid",
  "progress": 45,
  "sender": "user"
}
```

#### 5. Error Messages
```json
{
  "type": "error",
  "message": "Message too long (max 5000 characters)"
}
```

#### 6. Connection Acknowledgment
```json
{
  "type": "pong"
}
```

### Outgoing Events (To Server)

#### 1. Send Message
```json
{
  "type": "chat.message",
  "message": "I'm having trouble with my booking confirmation."
}
```

#### 2. Typing Indicators
```json
{
  "type": "typing.start"
}
```

```json
{
  "type": "typing.stop"
}
```

#### 3. File Upload Notification
```json
{
  "type": "file.upload.notify",
  "message_id": "msg-uuid"
}
```

#### 4. Keep-Alive Ping
```json
{
  "type": "ping"
}
```

## User Interface Features

### 1. Chat Initiation

#### Starting a Support Chat
- Users can initiate a chat from any page in the application
- Chat widget should be easily accessible (floating button or menu item)
- Automatic chat creation when user sends first message
- Welcome message display with expected response times

#### Chat Availability
- Display support hours and availability status
- Show estimated wait times during busy periods
- Offline message capability when support is unavailable
- Queue position indicator during high traffic

### 2. Message Interface

#### Message Display
- Clean, mobile-friendly chat interface
- Clear distinction between user and support messages
- Message timestamps and delivery status
- Auto-scroll to latest messages
- Message history persistence

#### Message Composition
- Simple text input with send button
- Character counter (max 5000 characters)
- Enter key to send (with Shift+Enter for new line)
- Emoji picker integration
- Auto-resize text area

#### Message Validation
- Real-time character count display
- Error messages for invalid content
- Automatic message trimming
- Prevent empty message submission

### 3. File Sharing Capabilities

#### Supported File Types
- Documents: PDF, DOC, DOCX, TXT
- Images: JPG, JPEG, PNG
- Maximum file size: 5MB per file

#### Upload Interface
- File selection button or drag-and-drop area
- File preview before sending
- Upload progress bar
- File size and type validation
- Multiple file selection support

#### File Display
- Thumbnail previews for images
- File name and size display
- Download links for attachments
- Error handling for failed uploads

### 4. Typing Indicators

#### User Experience
- Show when support agent is typing
- Display agent name if available
- Auto-hide after 10 seconds of inactivity
- Smooth animation effects

#### Implementation
- Send typing.start when user begins typing
- Send typing.stop when user stops or sends message
- Debounce typing events to avoid spam
- Clear indicator on page navigation

### 5. Status and Notifications

#### Chat Status Display
- Current chat status (pending, in_progress, resolved)
- Support agent availability
- Response time estimates
- Queue position when applicable

#### Notification System
- Browser notifications for new messages (with permission)
- Sound alerts for incoming messages
- Visual indicators for unread messages
- Badge counts on chat widget

#### Connection Status
- WebSocket connection indicator
- Reconnection attempts display
- Offline mode handling
- Error state notifications

## REST API Integration

### Chat Management

#### Get or Create User Chat
- **GET** `/support/chats/` (returns user's chat if exists)
- **POST** `/support/chats/` (creates new chat if needed)
- **Response**: Chat details with ID and status

#### Get Chat Messages
- **GET** `/support/messages/?chat_id={chat_id}`
- **Response**: Paginated message history
- **Pagination**: Load older messages on scroll

### Message Operations

#### Send Message with Attachments
- **POST** `/support/messages/`
- **Content-Type**: `multipart/form-data`
- **Body**: Form data with message text and optional files
- **Response**: Created message with ID

#### Upload Progress Tracking
- **POST** `/support/messages/upload_progress/`
- **Body**: `{"message_id": "uuid", "progress": 50}`
- **Use**: Track file upload progress

## Error Handling

### Connection Errors
- WebSocket connection failures
- Authentication token expiration
- Network connectivity issues
- Server maintenance periods

### Validation Errors
- Message length exceeded
- Unsupported file types
- File size too large
- Rate limiting violations

### User-Friendly Error Messages
- "Your message is too long. Please keep it under 5000 characters."
- "File type not supported. Please use PDF, DOC, DOCX, TXT, JPG, JPEG, or PNG."
- "File size too large. Maximum size is 5MB."
- "You're sending messages too quickly. Please wait a moment."

## Performance Optimization

### Message Loading
- Implement pagination for message history
- Load recent messages first
- Lazy load older messages on scroll
- Cache messages locally for offline viewing

### File Handling
- Compress images before upload when possible
- Show upload progress for large files
- Implement retry mechanism for failed uploads
- Cache file previews locally

### Real-time Updates
- Efficient WebSocket message handling
- Debounce typing indicators
- Batch multiple rapid updates
- Optimize re-rendering of message lists

## Mobile Responsiveness

### Design Considerations
- Touch-friendly interface elements
- Responsive layout for various screen sizes
- Optimized keyboard handling
- Swipe gestures for navigation

### Mobile-Specific Features
- Camera integration for photo uploads
- File picker integration
- Push notifications support
- Background connection handling

## Accessibility

### Screen Reader Support
- Proper ARIA labels for all interactive elements
- Semantic HTML structure
- Alt text for images and attachments
- Keyboard navigation support

### Visual Accessibility
- High contrast mode support
- Scalable text and interface elements
- Color-blind friendly design
- Focus indicators for keyboard navigation

## Security Considerations

### Input Validation
- Client-side validation for message content
- File type and size validation
- XSS prevention in message display
- CSRF protection for API calls

### Data Protection
- Secure file upload handling
- Encrypted WebSocket connections (WSS)
- Token-based authentication
- Automatic session timeout

## Testing Requirements

### Functional Testing
- Message sending and receiving
- File upload and download
- Typing indicator functionality
- Error handling scenarios

### Cross-Browser Testing
- WebSocket compatibility
- File API support
- Notification API support
- Mobile browser testing

### Performance Testing
- Large message history handling
- Multiple file uploads
- Connection stability
- Memory usage optimization

## Implementation Checklist

### Basic Features
- [ ] WebSocket connection establishment
- [ ] Message sending and receiving
- [ ] Basic error handling
- [ ] Connection status display

### Advanced Features
- [ ] File upload functionality
- [ ] Typing indicators
- [ ] Desktop notifications
- [ ] Message history pagination

### Polish Features
- [ ] Emoji support
- [ ] Message timestamps
- [ ] Read receipts
- [ ] Offline mode handling

### Testing
- [ ] Unit tests for core functionality
- [ ] Integration tests with backend
- [ ] Cross-browser compatibility
- [ ] Mobile responsiveness
- [ ] Accessibility compliance
