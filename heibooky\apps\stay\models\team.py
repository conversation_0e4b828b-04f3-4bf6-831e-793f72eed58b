from django.db import models
import uuid
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.users.models import User

class PropertyPermission(models.Model):
    """
    Model to define available permissions for property staff members.
    """
    PERMISSION_CHOICES = [
        ('property_config', 'Property Configuration'),
        ('booking_manage', 'Booking Management'),
        ('rates_manage', 'Rates Management'),
        ('room_config', 'Room Configuration'),
        ('view_only', 'View Only'),
        ('cleaning_staff', 'Cleaning Staff'),
    ]

    name = models.CharField(
        max_length=50, 
        choices=PERMISSION_CHOICES, 
        unique=True,
        db_index=True,
        error_messages={
            'unique': _("This permission type already exists."),
        }
    )
    description = models.TextField()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name'],
                name='unique_property_permission'
            )
        ]

    def __str__(self):
        return self.get_name_display()

class StaffRole(models.Model):
    """
    Model to define staff roles and their associated permissions.
    """
    property = models.ForeignKey('stay.Property', on_delete=models.CASCADE, related_name='staff_roles')
    user = models.ForeignKey('users.User', on_delete=models.CASCADE, related_name='property_roles')
    permissions = models.ManyToManyField(PropertyPermission, related_name='staff_roles')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('property', 'user')

    def __str__(self):
        return f"{self.user.email} - {self.property.name}"

    def clean(self):
        """Validate permission combinations."""
        
        # Skip validation if this is a new instance
        if not self.pk:
            return
            
        if not hasattr(self, 'permissions'):
            return
            
        permissions = self.permissions.all()
        has_cleaning = any(p.name == 'cleaning_staff' for p in permissions)
        
        if has_cleaning and permissions.count() > 1:
            raise ValidationError({
                'permissions': _("'cleaning_staff' permission cannot be combined with other permissions.")
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class TeamInvite(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey('stay.Property', on_delete=models.CASCADE, related_name='invites')
    user = models.ForeignKey('users.User', on_delete=models.CASCADE, null=True, blank=True, related_name='team_invites')
    email = models.EmailField()
    invited_by = models.ForeignKey('users.User', on_delete=models.CASCADE)
    permissions = models.ManyToManyField(PropertyPermission, related_name='invites')
    is_registered = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    accepted = models.BooleanField(default=False)
    accepted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ('property', 'email')
        indexes = [
            models.Index(fields=['email', 'accepted']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Invite for {self.email} to {self.property.name}"

    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def clean(self):
        """Validate permission combinations."""                   
        if not hasattr(self, 'permissions'):
            return
            
        permissions = self.permissions.all()
        has_cleaning = any(p.name == 'cleaning_staff' for p in permissions)
        
        if has_cleaning and permissions.count() > 1:
            raise ValidationError({
                'permissions': _("'cleaning_staff' permission cannot be combined with other permissions.")
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)