import uuid
import os
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from apps.users.models import User
from django.core.exceptions import ValidationError
import os

class Chat(models.Model):
    """Model to store support chats between users and admins"""
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('resolved', _('Resolved')),
        ('sent', _('Sent')),
    )
    
    PRIORITY_CHOICES = (
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_chats')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_message_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-last_message_at']
        verbose_name = _('Support Chat')
        verbose_name_plural = _('Support Chats')
    
    def __str__(self):
        return f"Chat with {self.user.name} - {self.status}"
    
    @property
    def last_message(self):
        """Return the most recent message in this chat"""
        return self.messages.order_by('-created_at').first()
    
    @classmethod
    def get_or_create_chat(cls, user):
        """Get existing chat for user or create a new one"""
        chat, created = cls.objects.get_or_create(
            user=user,
            defaults={
                'status': 'pending',
                'priority': 'medium'
            }
        )
        return chat

class SupportMessage(models.Model):
    """Model to store support chat messages"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    chat = models.ForeignKey(Chat, on_delete=models.CASCADE, related_name='messages')
    message = models.TextField()
    is_from_support = models.BooleanField(default=False, help_text=_('True if sent by support staff'))
    is_read = models.BooleanField(default=False, help_text=_('True if message has been read'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['created_at']
        verbose_name = _('Support Message')
        verbose_name_plural = _('Support Messages')
        
    def __str__(self):
        sender = "Support" if self.is_from_support else self.chat.user.name
        return f"{sender} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    @property
    def sender(self):
        """Return the sender type based on the is_from_support field"""
        return "support" if self.is_from_support else "user"
    def save(self, *args, **kwargs):
        """Update the chat's last_message_at field when a new message is saved"""
        super().save(*args, **kwargs)
        # Update the chat's last_message_at field
        self.chat.last_message_at = self.created_at
        self.chat.save(update_fields=['last_message_at'])
    
    @classmethod
    def get_response_time_stats(cls, start_date=None, end_date=None):
        """Calculate response time statistics for support messages"""
        from django.utils import timezone
        import datetime
        
        # Default to the last 30 days if not specified
        if not start_date:
            start_date = timezone.now() - datetime.timedelta(days=30)
        
        if not end_date:
            end_date = timezone.now()
            
        # Get all user messages in the time range
        user_messages = cls.objects.filter(
            is_from_support=False,
            created_at__range=(start_date, end_date)
        ).order_by('chat__user', 'created_at')
        
        response_times = []
        
        for msg in user_messages:
            # Find the next support response after this user message
            next_response = cls.objects.filter(
                chat=msg.chat,
                is_from_support=True,
                created_at__gt=msg.created_at
            ).order_by('created_at').first()
            
            if next_response:
                # Calculate time difference in minutes
                time_diff = (next_response.created_at - msg.created_at).total_seconds() / 60
                response_times.append(time_diff)
        
        # Calculate statistics
        stats = {
            'count': len(response_times),
            'average': round(sum(response_times) / len(response_times)) if response_times else 0,
            'min': round(min(response_times)) if response_times else 0,
            'max': round(max(response_times)) if response_times else 0,
        }
        
        return stats

class MessageAttachment(models.Model):
    """Model to store attachments for support messages"""
    ALLOWED_EXTENSIONS = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'txt']
    MAX_SIZE = 5 * 1024 * 1024  # 5MB
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SupportMessage, on_delete=models.CASCADE, related_name='attachments')
    file = models.FileField(upload_to='support_attachments/')
    file_name = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField(default=0, help_text=_('File size in bytes'))
    content_type = models.CharField(max_length=100, blank=True, help_text=_('MIME type of the file'))
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['created_at']
        verbose_name = _('Message Attachment')
        verbose_name_plural = _('Message Attachments')
        
    def __str__(self):
        return self.file_name
    
    def clean(self):
        """Enhanced validation for file attachments"""
        super().clean()
        
        if self.file:
            # Check file extension
            file_extension = os.path.splitext(self.file.name)[1].lower().lstrip('.')
            if file_extension not in self.ALLOWED_EXTENSIONS:
                raise ValidationError({
                    'file': f'File type "{file_extension}" is not allowed. Allowed types: {", ".join(self.ALLOWED_EXTENSIONS)}'
                })
            
            # Check file size
            if self.file.size > self.MAX_SIZE:
                size_mb = self.MAX_SIZE / (1024 * 1024)
                raise ValidationError({
                    'file': f'File size exceeds {size_mb}MB limit'
                })
            
            # Check for malicious files (basic MIME type validation)
            if hasattr(self.file, 'content_type'):
                allowed_mime_types = [
                    'application/pdf', 'image/jpeg', 'image/png', 'image/jpg',
                    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'text/plain'
                ]
                if self.file.content_type not in allowed_mime_types:
                    raise ValidationError({
                        'file': f'MIME type "{self.file.content_type}" is not allowed'
                    })
