import string
import secrets

def generate_unique_id(model_name, length=10, max_retries=10):
    """Generates a unique ID for reservation model during manual booking field of BookingBlock."""
    for attempt in range(max_retries):
        result_str = ''.join(secrets.choice(string.ascii_letters + string.digits) for i in range(length))
        if not model_name.objects.filter(id=result_str).exists():
            return result_str
    raise ValueError("Failed to generate unique ID after multiple attempts")