#!/usr/bin/env python
"""
Complete integration test for the room onboarding inventory system.
This test validates the entire workflow:
1. Room becomes onboarded
2. Signal triggers inventory setup task
3. Management command can update all onboarded rooms
"""

import os
import sys
import django
from unittest.mock import patch, MagicMock

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django.setup()

from apps.stay.models import Room, Property
from apps.booking.tasks.inventory import set_room_inventory_for_year
from django.core.management import call_command
from io import StringIO

def test_complete_integration():
    """Test the complete integration of room onboarding to inventory setup"""
    
    print("🚀 Starting Complete Integration Test")
    print("=" * 60)
    
    # Mock the actual SU API call to avoid making real API requests
    with patch('services.su_api.su_api.update_inventory') as mock_api:
        mock_api.return_value = {'success': True, 'message': 'Inventory updated successfully'}
        
        # Mock the Celery task delay method to capture calls
        with patch('apps.booking.tasks.inventory.set_room_inventory_for_year.delay') as mock_task:
            mock_task.return_value = MagicMock(id='test-task-id')
            
            try:
                # Step 1: Find or create a test room
                print("📋 Step 1: Setting up test room...")
                room = Room.objects.filter(is_onboarded=False).first()
                
                if not room:
                    # Use an existing onboarded room and temporarily set it to False
                    room = Room.objects.filter(is_onboarded=True).first()
                    if not room:
                        print("❌ No room found for testing")
                        return False
                    
                    print("📋 Using existing room, temporarily setting to not onboarded")
                    room.is_onboarded = False
                    room.save()
                
                # Ensure the property is also onboarded for management command testing
                original_property_onboarded = room.property.is_onboarded
                room.property.is_onboarded = True
                room.property.save()
                
                print(f"📋 Test room ID: {room.id}")
                print(f"📋 Initial onboarding status: {room.is_onboarded}")
                print(f"📋 Room quantity: {room.quantity}")
                
                # Step 2: Test signal triggering
                print("\n📋 Step 2: Testing signal trigger...")
                room.is_onboarded = True
                room.save()
                
                if mock_task.called:
                    print("✅ Signal triggered successfully!")
                    print(f"📞 Task called with room ID: {mock_task.call_args[0][0]}")
                    signal_test_passed = True
                else:
                    print("❌ Signal was not triggered!")
                    signal_test_passed = False
                
                # Step 3: Test actual task execution (synchronously)
                print("\n📋 Step 3: Testing task execution...")
                mock_task.reset_mock()  # Reset the mock to avoid confusion
                try:
                    # Call the task directly (synchronously) to test its functionality
                    result = set_room_inventory_for_year(str(room.id))
                      # Check if the task completed successfully by examining the response
                    if (result and 
                        result.get('status') == 'success' and 
                        result.get('response', {}).get('Status') == 'Success'):
                        print("✅ Task executed successfully!")
                        print(f"📊 Task result: {result}")
                        task_test_passed = True
                    else:
                        print("❌ Task execution failed!")
                        print(f"📊 Task result: {result}")
                        task_test_passed = False
                        
                except Exception as e:
                    print(f"❌ Task execution failed with error: {str(e)}")
                    task_test_passed = False
                
                # Step 4: Test management command
                print("\n📋 Step 4: Testing management command...")
                try:
                    # Capture command output
                    out = StringIO()
                    call_command('update_room_inventory', '--dry-run', stdout=out)
                    output = out.getvalue()
                    if 'onboarded rooms to update' in output and str(room.id) in output:
                        print("✅ Management command found the onboarded room!")
                        print("📊 Command output (excerpt):")
                        for line in output.split('\n'):
                            if line.strip():
                                print(f"   {line}")
                        management_test_passed = True
                    else:
                        print("❌ Management command did not find the room!")
                        print(f"📊 Command output: {output}")
                        management_test_passed = False
                        
                except Exception as e:
                    print(f"❌ Management command failed: {str(e)}")
                    management_test_passed = False
                
                # Step 5: Clean up
                print("\n📋 Step 5: Cleaning up...")
                room.is_onboarded = False  # Reset to original state
                room.property.is_onboarded = original_property_onboarded
                room.save()
                room.property.save()
                print("🔄 Test data restored to original state")
                
                # Overall result
                all_tests_passed = signal_test_passed and task_test_passed and management_test_passed
                
                print("\n" + "=" * 60)
                print("📊 INTEGRATION TEST RESULTS:")
                print(f"   Signal triggering: {'✅ PASS' if signal_test_passed else '❌ FAIL'}")
                print(f"   Task execution: {'✅ PASS' if task_test_passed else '❌ FAIL'}")
                print(f"   Management command: {'✅ PASS' if management_test_passed else '❌ FAIL'}")
                
                if all_tests_passed:
                    print("\n🎉 ALL INTEGRATION TESTS PASSED!")
                    print("   The complete room onboarding inventory system is working correctly.")
                else:
                    print("\n💥 Some integration tests failed.")
                
                return all_tests_passed
                
            except Exception as e:
                print(f"❌ Integration test failed with error: {str(e)}")
                return False

if __name__ == "__main__":
    success = test_complete_integration()
    sys.exit(0 if success else 1)
