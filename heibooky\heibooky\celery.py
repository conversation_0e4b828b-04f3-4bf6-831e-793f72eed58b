import os
from celery import Celery
from django.conf import settings
from kombu.utils.url import maybe_sanitize_url
from celery.signals import celeryd_after_setup
import logging

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')

app = Celery('heibooky')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

# SSL and Connection Settings
app.conf.update(
    broker_url=settings.CELERY_BROKER_URL,
    result_backend=settings.CELERY_RESULT_BACKEND,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
    broker_connection_timeout=30,
    broker_transport_options={
        'visibility_timeout': 3600,
        'max_retries': 3,
        'interval_start': 0,
        'interval_step': 0.2,
        'interval_max': 0.5,
    },
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=50,
    worker_max_memory_per_child=50000  # 50MB
)

# Log Redis connection details
@celeryd_after_setup.connect
def log_redis_connection(sender, instance, **kwargs):
    logger = logging.getLogger(__name__)
    logger.info(f"Connecting to Redis at: {maybe_sanitize_url(settings.REDIS_URL)}")

# Include tasks from all registered Django apps
app.autodiscover_tasks()

# Important: Explicitly register task modules
app.conf.imports = [
    'apps.booking.tasks',
    'apps.integrations.tasks.payments',
    'apps.pricing.tasks',
    'apps.integrations.tasks.online_checkin',
    'apps.stay.tasks',
    'apps.support.tasks',
]

# Configure periodic tasks
app.conf.beat_schedule = {
    'sync-guest-data-daily': {
        'task': 'apps.integrations.tasks.online_checkin.sync_guest_data',
        'schedule': 86400.0,  # Run once a day (in seconds)
        'options': {'expires': 3600}  # Task expires after 1 hour
    },
}
