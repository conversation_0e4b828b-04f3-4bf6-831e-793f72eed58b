# Generated by Django 5.1.2 on 2025-04-12 13:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('billing', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='billingprofile',
            name='owner',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='billingaddress',
            name='billing_profile',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='billing_address', to='billing.billingprofile'),
        ),
        migrations.AddField(
            model_name='taxation',
            name='billing_profile',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='taxation', to='billing.billingprofile'),
        ),
    ]
